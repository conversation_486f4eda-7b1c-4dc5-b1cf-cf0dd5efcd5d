# 技术经理组长绩效指标计算详细分析文档

## 1. 绩效系统架构概述

### 1.1 绩效计算流程
整个绩效系统分为三个阶段：

#### 第一阶段：绩效反馈生成
- **数据来源**：各种原始业务数据（代码行数、考勤记录、任务数据等）
- **处理逻辑**：系统根据原始数据计算各种绩效指标的具体数值
- **存储位置**：`tb_performance_feedback` 表
- **数据特点**：包含具体的数值和推荐等级

#### 第二阶段：个人绩效指标计算
- **数据来源**：`tb_performance_feedback` 表
- **处理逻辑**：基于反馈数据计算每个人每个指标的最终等级
- **存储位置**：`tb_performance_indicator` 表  
- **数据特点**：每个人每个指标一个确定的等级（S/A/C/D），**注意：B级绩效不会记录在此表中**

#### 第三阶段：技术经理团队管理指标计算
- **数据来源**：`tb_performance_indicator` 表（已计算好的等级） + 部分原始数据
- **处理逻辑**：基于团队成员的等级计算技术经理的团队管理指标
- **处理类**：`IndicatorLevelCalcService` 的各种实现类
- **数据特点**：技术经理的等级基于团队成员表现

### 1.2 技术经理指标调用链

#### 调用入口
**定时任务**：`PerformanceIndicatorTask.calcPerformanceIndicatorJobHandler`

#### 完整执行流程
```java
@XxlJob("calcPerformanceIndicatorJobHandler")
public ReturnT<String> calcPerformanceIndicator(String param) {
    // 1. 组员绩效指标计算（基于反馈数据）
    performanceGenerator.generatePerformanceFromFeedback(year, month, 0);
    
    // 2. 组长绩效反馈生成（基于组员指标等级）
    performanceFeedbackManager.generateFeedbackByYearMonth(year, month, 1);
    
    // 3. 组长绩效指标计算（基于组长反馈数据）
    performanceGenerator.generatePerformanceFromFeedback(year, month, 1);
}
```

#### 技术经理指标计算时机
技术经理指标在**第2步**中计算，调用链为：
```
PerformanceFeedbackManager.generateFeedbackByYearMonth(year, month, 1)
  └─ generateFeedbackForEmployee(nickName, year, month, PersonTypeEnum.TECHNICAL_MANAGER, trackWorkResults)
      └─ registry.getAllByRole(PersonTypeEnum.TECHNICAL_MANAGER) // 获取技术经理指标服务列表
          └─ IndicatorLevelCalcService.calculateLevel() // 调用具体指标计算
              └─ IndicatorLevelCalcService.createLogContent() // 生成日志内容
```

### 1.3 技术经理指标特点
技术经理的绩效指标与普通员工不同：
- **不基于个人业务表现**：技术经理不直接产出代码、测试用例等
- **基于团队管理效果**：通过团队成员的表现来评估管理水平
- **团队表现聚合**：需要将多个成员的指标等级聚合成经理的等级
- **两种计算模式**：纯等级聚合 + 复合数据计算

## 2. 重要修复：B级绩效处理问题

### 2.1 问题描述

在原始设计中存在一个重要问题：**B级绩效不会记录在`tb_performance_indicator`表中**。

- **绩效反馈生成阶段**：在`PerformanceFeedbackManager.generateFeedbackForEmployee()`中，B级绩效会被跳过：
  ```java
  if (ScoreLevelEnum.SCORE_B.getCode().equals(feedback.getRecommendedLevel())) {
      // 合格绩效不生成绩效反馈
      continue;
  }
  ```
- **绩效指标计算阶段**：由于B级不生成反馈记录，在转换为绩效指标时也不会有记录
- **技术经理绩效计算问题**：技术经理绩效计算依赖查询`tb_performance_indicator`表，原逻辑无法正确统计B级成员

### 2.2 解决方案

**修复原则**：对于有绩效记录但没有指标记录的成员，视为B级绩效。

#### 2.2.1 GroupWorkEfficiencyManageIndicatorServiceImpl 修复

**修复前的问题**：
```java
// 原逻辑只统计了有指标记录的成员，B级成员被忽略
private Map<String, String> getMemberEfficiencyLevels(List<Long> performanceIds, CalculationCache cache) {
    List<PerformanceIndicator> efficiencyIndicators = performanceIndicatorService.list(/*...*/);
    // 只处理有记录的成员
    for (PerformanceIndicator indicator : efficiencyIndicators) { /*...*/ }
}
```

**修复后的逻辑**：
```java
private Map<String, String> getMemberEfficiencyLevels(List<Long> performanceIds, CalculationCache cache) {
    // 1. 查询已有的工作效率指标记录（只包含S/A/C/D级）
    List<PerformanceIndicator> efficiencyIndicators = /*...*/;
    
    // 2. 查询所有团队成员的绩效记录
    List<Performance> allPerformances = performanceService.list(/*...*/);
    
    // 3. 对于有绩效记录但没有指标记录的成员，视为B级
    for (Performance performance : allPerformances) {
        if (memberLevel != null) {
            memberLevels.put(memberNickName, memberLevel); // 有记录的等级
        } else {
            memberLevels.put(memberNickName, ScoreLevelEnum.SCORE_B.getCode()); // 视为B级
        }
    }
}
```

#### 2.2.2 GroupResultOutputIndicatorServiceImpl 修复

**修复前的问题**：
```java
// 原逻辑对于没有指标记录的成员返回null，导致被排除
private String calculateMemberLevel(SysUser member, List<Long> performanceIds) {
    // ...查询逻辑...
    if (memberIndicators.isEmpty()) {
        return null; // 问题：应该返回B级而不是null
    }
}
```

**修复后的逻辑**：
```java
private String calculateMemberLevelWithBSupport(SysUser member, List<Long> performanceIds, 
                                               Map<Long, String> performanceIdToNickName) {
    // ...查询逻辑...
    if (memberIndicators.isEmpty()) {
        // 有绩效记录但没有指标记录，视为B级
        return ScoreLevelEnum.SCORE_B.getCode();
    }
}
```

### 2.3 修复影响

**修复前**：
- B级成员在等级统计中被忽略
- 团队人数统计不准确
- 等级分布计算错误

**修复后**：
- 所有团队成员都被正确统计
- B级成员被正确识别和计入
- 技术经理绩效等级计算更加准确

## 3. 已实现指标分析

### 3.1 团队平均月考勤指标 (GroupAvgAttendanceIndicatorServiceImpl)

#### 业务逻辑
- **指标代码**：`GROUP_AVG_ATTENDANCE`
- **计算对象**：技术经理本人
- **计算模式**：**复合数据计算**（既用等级又用原始数据）
- **数据来源**：
  1. 团队成员的考勤指标等级（从 `tb_performance_indicator` 表）
  2. 团队成员的考勤原始数据（从 `tb_user_kq_stat` 表）
  3. 部门工时排名数据（从 `performStatService.getWorkTotal()` 接口）

#### 计算规则（按优先级）
1. **D级**：如果团队中任何一个成员的考勤指标是D级 → 技术经理直接评为D级（一票否决）
2. **C级**：团队整体早退或旷工≥3次 或 迟到≥11次
3. **S级**：平均工时≥8小时 且 部门排名前10% 且 无早退 且 迟到≤3次
4. **A级**：平均工时≥8小时 且 部门排名前20% 且 无早退 且 迟到≤5次
5. **B级**：平均工时≥7小时 且 无早退 且 迟到≤10次

#### 技术实现要点
```java
// 1. 一票否决检查（从PerformanceIndicator表）
List<Long> performanceIds = performanceService.list(/* 查询团队绩效记录 */);
List<PerformanceIndicator> performanceIndicators = performanceIndicatorService.list(
    /* 过滤考勤指标 */);
for (PerformanceIndicator indicator : performanceIndicators) {
    if (ScoreLevelEnum.SCORE_D.getCode().equals(indicator.getScoreLevel())) {
        return ScoreLevelEnum.SCORE_D.getCode(); // 一票否决
    }
}

// 2. 原始考勤数据统计（从UserKqStat表）
List<UserKqStat> teamAttendanceStats = userKqStatService.listByUserNamesAndKqYearAndKqMonth(/*...*/);
Long totalLateCount = teamAttendanceStats.stream().map(UserKqStat::getKqLateCount).reduce(0L, Long::sum);

// 3. 部门排名计算（从外部接口）
R<List<WorkTotalVO>> rWorkTotal = performStatService.getWorkTotal(year, month);
boolean isTopPercent = isTopPercent(rWorkTotal.getData(), workTotal, CommConstants.CommonVal.ONE_TENTH);
```

#### 特殊规则
- **一票否决机制**：任何成员D级，经理直接D级
- **复合计算**：结合等级数据、考勤数据、排名数据
- **常量使用**：使用 `CommConstants.CommonVal.*` 避免魔法值
- **异常处理**：默认返回B级

### 3.2 月结果产出指标 (GroupResultOutputIndicatorServiceImpl)

#### 业务逻辑
- **指标代码**：`GROUP_RESULT_OUTPUT`
- **计算对象**：技术经理本人
- **计算模式**：**纯等级聚合**（只使用等级数据）
- **数据来源**：团队成员的专业指标等级（从 `tb_performance_indicator` 表）

### 3.3 本组组员平均月新增工作相关文档指标 (GroupAvgWorkDocsIndicatorServiceImpl)

#### 业务逻辑
- **指标代码**：`GROUP_AVG_WORK_DOCS`
- **计算对象**：技术经理本人
- **计算模式**：**复合数据计算**（既用等级又用原始数据）
- **数据来源**：
  1. 团队成员的文档指标等级（从 `tb_performance_indicator` 表，用于一票否决）
  2. 团队成员的实际文档数据（从 `IZtDocService` 接口，用于计算平均数）

### 3.4 组月绩效预警数指标 (GroupPerformanceWarnIndicatorServiceImpl)

#### 业务逻辑
- **指标代码**：`GROUP_PERFORMANCE_WARN`
- **计算对象**：技术经理本人
- **计算模式**：**复合数据计算**（既用等级又用原始数据）
- **数据来源**：
  1. 团队成员的预警指标等级（从 `tb_performance_indicator` 表，用于一票否决）
  2. 团队成员的实际预警数据（从 `WarnRecord` 表，用于统计P0、P1、P2预警次数）

#### 计算规则（按优先级）
1. **D级**：如果团队中任何一个成员的预警指标是D级 → 技术经理直接评为D级（一票否决）
2. **C级**：P0预警>=2次
3. **B级**：无P0预警并且P1预警<5次并且P2预警<8次
4. **A级**：无P0预警
5. **S级**：无P0、P1预警

#### 技术实现要点
```java
// 1. 一票否决检查（从PerformanceIndicator表）
List<PerformanceIndicator> warnIndicators = performanceIndicatorService.list(
    new LambdaQueryWrapper<PerformanceIndicator>()
        .in(PerformanceIndicator::getPerformanceId, performanceIds)
        .eq(PerformanceIndicator::getIndicatorCode, PerformanceIndicatorEnum.PERFORMANCE_WARN.getCode()));
if (ScoreLevelEnum.SCORE_D.getCode().equals(indicator.getScoreLevel())) {
    return ScoreLevelEnum.SCORE_D.getCode(); // 一票否决
}

// 2. 统计团队预警数据（查询实际预警记录）
List<WarnRecord> memberWarns = warnRecordMapper.selectList(
    new LambdaQueryWrapper<WarnRecord>()
        .eq(WarnRecord::getUserId, userId)
        .between(WarnRecord::getCreateTime, start, end));

// 3. 按级别统计预警数量
int p0Count = countWarnRecordsByLevel(memberWarns, WarnLevelEnum.P0);
int p1Count = countWarnRecordsByLevel(memberWarns, WarnLevelEnum.P1);
int p2Count = countWarnRecordsByLevel(memberWarns, WarnLevelEnum.P2);
```

#### 特殊规则
- **一票否决机制**：任何成员预警指标D级，经理直接D级
- **复合计算**：结合等级数据和原始预警数据
- **分级别统计**：分别统计P0、P1、P2预警次数
- **团队汇总**：统计所有团队成员的预警总数

### 3.5 工作效率管理指标 (WorkEfficiencyManageIndicatorServiceImpl)

#### 业务逻辑
- **指标代码**：`WORK_EFFICIENCY_MANAGE`
- **计算对象**：技术经理本人
- **计算模式**：**复合数据计算**（等级聚合 + 绩效事件登记检查）
- **数据来源**：
  1. 团队成员的工作效率指标等级（从 `tb_performance_indicator` 表，查询DEV_EFFICIENCY指标）
  2. 工作效率相关的绩效事件登记记录（从 `tb_performance_feedback_main` 表，查询两类指标）：
     - **WORK_EFFICIENCY_MANAGE**：组长的工作效率管理绩效事件
     - **DEV_EFFICIENCY**：组员的工作效率绩效事件  
     - 两者合并判断是否有登记情况

#### 计算规则（按优先级）
1. **D级**：组内有任何一个得D绩效 或 连续2月本指标无任何绩效事件登记
2. **C级**：组内得C的人占一半或以上 或 本月本指标无任何绩效事件登记
3. **B级**：组内人数（含组长）>=4，要求半数以上为B以上；组内人数<4，要求全部为B以上
4. **A级**：组内人数（含组长）>=4，要求半数以上为A或S；组内人数<4，要求全部为A或S
5. **S级**：组内人数（含组长）>=4，要求半数以上为S；组内人数<4，要求全部为S

#### 技术实现要点
```java
// 1. 一票否决检查（从PerformanceIndicator表）
List<PerformanceIndicator> efficiencyIndicators = performanceIndicatorService.list(
    new LambdaQueryWrapper<PerformanceIndicator>()
        .in(PerformanceIndicator::getPerformanceId, performanceIds)
        .eq(PerformanceIndicator::getIndicatorCode, PerformanceIndicatorEnum.DEV_EFFICIENCY.getCode()));
if (ScoreLevelEnum.SCORE_D.getCode().equals(indicator.getScoreLevel())) {
    return ScoreLevelEnum.SCORE_D.getCode(); // 一票否决
}

// 2. 检查绩效事件登记（查询两类工作效率指标）
private List<PerformanceFeedback> getWorkEfficiencyEvents(Integer year, Integer month, List<String> teamNickNames) {
    // 查询组长的工作效率管理指标事件
    List<PerformanceFeedback> manageEvents = performanceFeedbackMainService.getBySecondaryIndicator(
        PerformanceIndicatorEnum.WORK_EFFICIENCY_MANAGE.getCode(), year, month, teamNickNames);
    
    // 查询组员的工作效率指标事件
    List<PerformanceFeedback> devEvents = performanceFeedbackMainService.getBySecondaryIndicator(
        PerformanceIndicatorEnum.DEV_EFFICIENCY.getCode(), year, month, teamNickNames);
    
    // 合并两个查询结果
    List<PerformanceFeedback> allEvents = new ArrayList<>();
    if (manageEvents != null) allEvents.addAll(manageEvents);
    if (devEvents != null) allEvents.addAll(devEvents);
    return allEvents;
}

// 3. 月份跨年处理
int prevMonth = month - 1;
int prevYear = year;
if (prevMonth <= 0) {
    prevMonth = 12;
    prevYear = year - 1;
}

// 4. 团队规模分层计算
if (teamSize >= 4) {
    int halfSize = teamSize / 2;
    if (sLevelCount > halfSize) return ScoreLevelEnum.SCORE_S.getCode();
    // ... 其他等级判断
} else {
    if (sLevelCount == totalWithLevels) return ScoreLevelEnum.SCORE_S.getCode();
    // ... 其他等级判断
}
```

#### 特殊规则
- **绩效事件登记检查**：使用`IPerformanceFeedbackService.getMainBySecondaryIndicator()`查询指定指标的绩效反馈记录
- **连续月份检查**：正确处理跨年的月份边界问题
- **团队规模分层**：根据团队人数（>=4 vs <4）采用不同的等级计算标准
- **多重条件优先级**：按D级→C级→正常等级的顺序进行判断
- **等级分布统计**：详细统计S/A/B/C各级人数和占比

#### 计算规则（按优先级）
1. **D级**：如果团队中任何一个成员的文档指标是D级 → 技术经理直接评为D级（一票否决）
2. **S级**：团队平均至少3篇有效文档（字数≥200字）
3. **A级**：团队平均至少2篇有效文档（字数≥200字）
4. **B级**：团队平均至少1篇有效文档（字数≥200字）
5. **C级**：团队平均0篇有效文档（当月无）

#### 技术实现要点
```java
// 1. 一票否决检查（从PerformanceIndicator表）
List<PerformanceIndicator> docIndicators = performanceIndicatorService.list(
    new LambdaQueryWrapper<PerformanceIndicator>()
        .in(PerformanceIndicator::getPerformanceId, performanceIds)
        .eq(PerformanceIndicator::getIndicatorCode, PerformanceIndicatorEnum.WORK_DOCS.getCode()));
if (ScoreLevelEnum.SCORE_D.getCode().equals(indicator.getScoreLevel())) {
    return ScoreLevelEnum.SCORE_D.getCode(); // 一票否决
}

// 2. 计算团队平均文档数（查询实际文档数据）
List<ZtDocVo> memberDocs = ztDocService.getUserDocuments(memberName, startTime, endTime);
List<ZtDocVo> validDocs = memberDocs.stream()
    .filter(doc -> getWordCount(doc.getDraft()) >= MIN_WORD_COUNT)
    .collect(Collectors.toList());

// 3. 文档字数统计（使用Jsoup解析HTML内容）
String textOnly = Jsoup.parse(htmlContent).text();
return textOnly.replaceAll("\\s", "").length();
```

#### 特殊规则
- **一票否决机制**：任何成员文档指标D级，经理直接D级
- **复合计算**：结合等级数据和原始文档数据
- **字数过滤**：只统计字数≥200的有效文档
- **平均数计算**：排除经理本人，只计算团队成员

#### 成员角色映射规则
- **开发成员**：查询"代码行数"指标等级
- **测试成员**：查询"用例执行数"+"BUG创建数"指标等级，使用`ScoreLevelUtil.determineCategoryLevel`合并
- **运维成员**：查询"计划性维护数"指标等级
- **其他角色**：忽略

#### 人数比例评级规则
- **D级**：组内有任何一个得D绩效（一票否决）
- **C级**：组内得C的人占一半或以上
- **人数≥4的团队**：
  - **S级**：要求半数以上为S
  - **A级**：要求半数以上为A或S
  - **B级**：要求半数以上为B以上
- **人数<4的团队**：
  - **S级**：要求全部为S
  - **A级**：要求全部为A或S
  - **B级**：要求全部为B以上

#### 技术实现要点
```java
// 1. 获取团队绩效记录ID
List<Long> performanceIds = performanceService.list(
    new LambdaQueryWrapper<Performance>()
        .eq(Performance::getYear, year)
        .eq(Performance::getMonth, month)
        .eq(Performance::getGroupId, deptId)
);

// 2. 根据成员角色查询对应指标等级
for (SysUser member : teamMembers) {
    List<String> indicators = getIndicatorsForMember(member); // 角色→指标映射
    List<PerformanceIndicator> memberIndicators = performanceIndicatorService.list(
        /* 过滤该成员的指标记录 */);
    String memberLevel = mergeMultipleLevels(levels); // 测试人员需要合并
}

// 3. 按人数比例规则计算最终等级
if (totalMembers >= CommConstants.CommonVal.FOUR) {
    return calculateLevelForLargeTeam(totalMembers, sCount, aCount, bCount);
} else {
    return calculateLevelForSmallTeam(totalMembers, sCount, aCount, bCount);
}
```

#### 测试人员特殊处理
```java
private String mergeMultipleLevels(List<String> levels) {
    double totalA = 0.0;
    int cCount = CommConstants.CommonVal.ZERO;
    for (String level : levels) {
        totalA += ScoreLevelUtil.convertLevelToAvalue(level);
        if (ScoreLevelEnum.SCORE_C.getCode().equals(level)) {
            cCount++;
        }
    }
    return ScoreLevelUtil.determineCategoryLevel(totalA, cCount);
}
```

## 4. 五种计算模式对比

| 对比维度 | 团队平均月考勤 | 月结果产出 | 本组组员平均月新增文档 | 组月绩效预警数 | 工作效率管理 |
|---------|---------------|------------|----------------------|--------------|-------------|
| **计算模式** | 复合数据计算 | 纯等级聚合 | 复合数据计算 | 复合数据计算 | 复合数据计算 |
| **数据来源** | 等级+原始数据+排名数据 | 仅等级数据 | 等级+文档数据 | 等级+预警数据 | 等级+事件登记数据 |
| **计算复杂度** | 高（多源数据融合） | 中（标准等级聚合） | 中（平均数计算） | 中（预警统计） | 中（等级分布+事件检查） |
| **角色区分** | 不区分角色 | 严格按角色区分指标 | 不区分角色 | 不区分角色 | 不区分角色 |
| **特殊规则** | 一票否决 + 排名计算 | 一票否决 + 人数比例 | 一票否决 + 字数过滤 | 一票否决 + 分级别统计 | 一票否决 + 事件登记检查 |
| **合并逻辑** | 复杂的业务规则 | 标准的等级合并 | 平均数计算 | 预警数统计 | 团队规模分层计算 |
| **常量使用** | ✅ 规范使用CommConstants | ✅ 规范使用CommConstants | ✅ 规范使用CommConstants | ✅ 规范使用CommConstants | ✅ 规范使用CommConstants |
| **方法拆分** | ❌ 较长的方法 | ✅ 良好的方法拆分 | ✅ 良好的方法拆分 | ✅ 良好的方法拆分 | ✅ 良好的方法拆分 |
| **异常处理** | 默认B级 | 默认B级 | 默认C级 | 默认C级 | 默认B级 |

## 5. 通用开发模式

### 5.1 标准开发流程

#### 1. 确定指标特性
- 是否有一票否决机制？
- 是否区分成员角色？
- 是否需要原始数据？
- 计算模式：纯等级聚合 vs 复合数据计算

#### 2. 查询团队绩效数据
```java
// 获取团队绩效记录ID（标准模式）
List<Long> performanceIds = performanceService.list(
    new LambdaQueryWrapper<Performance>()
        .eq(Performance::getYear, year)
        .eq(Performance::getMonth, month)
        .eq(Performance::getGroupId, deptId)
);
```

#### 3. 查询指标等级
```java
// 查询特定指标的等级（纯等级聚合模式）
List<PerformanceIndicator> indicators = performanceIndicatorService.list(
    new LambdaQueryWrapper<PerformanceIndicator>()
        .in(PerformanceIndicator::getPerformanceId, performanceIds)
        .eq(PerformanceIndicator::getIndicatorCode, targetIndicatorCode)
);
```

#### 4. 查询原始数据（如需要）
```java
// 查询原始业务数据（复合计算模式）
List<UserKqStat> attendanceStats = userKqStatService.listByUserNamesAndKqYearAndKqMonth(/*...*/);
R<List<WorkTotalVO>> workTotalData = performStatService.getWorkTotal(year, month);
```

#### 5. 应用业务规则
- 检查一票否决条件
- 应用等级聚合规则
- 处理特殊情况

#### 6. 缓存和日志
- 使用 ThreadLocal 缓存计算数据
- 在 `createLogContent` 中复用缓存
- 记录详细的计算过程

### 5.2 关键技术点

#### ThreadLocal 缓存模式
```java
private static final ThreadLocal<CalculationCache> CACHE = new ThreadLocal<>();

// 在 calculateLevel 中设置缓存
CACHE.set(cache);

// 在 createLogContent 中使用缓存
CalculationCache cache = CACHE.get();

// 在 finally 中清理缓存
CACHE.remove();
```

#### 常量使用规范
```java
// ✅ 正确使用常量
if (totalMembers >= CommConstants.CommonVal.FOUR) { ... }
if (dCount > CommConstants.CommonVal.ZERO) { ... }
int halfMembers = (totalMembers + CommConstants.CommonVal.ONE) / CommConstants.CommonVal.TWO;

// ❌ 不要使用魔法值
if (totalMembers >= 4) { ... }  // 错误
if (dCount > 0) { ... }         // 错误
```

#### 等级合并工具
- **单指标**：直接使用 `indicator.getScoreLevel()`
- **多指标**：使用 `ScoreLevelUtil.determineCategoryLevel(totalA, cCount)`

#### 方法拆分原则
```java
// ✅ 推荐：职责单一的小方法
private SysUser getManagerInfo(String nickName) { ... }
private List<SysUser> getTeamMembers(SysUser manager, String nickName) { ... }
private List<Long> getTeamPerformanceIds(Integer year, Integer month, Long deptId, String nickName) { ... }

// ❌ 避免：过长的方法
public String calculateLevel(/* 很多参数 */) {
    // 100+ 行的复杂逻辑
}
```

## 6. 开发指南

### 6.1 新建技术经理指标步骤

#### 1. 创建实现类
```java
@Slf4j
@Service
public class GroupXxxIndicatorServiceImpl implements IndicatorLevelCalcService {
    
    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.GROUP_XXX.getCode();
    }
}
```

#### 2. 实现核心方法
- `getIndicatorCode()`: 返回指标枚举代码
- `calculateLevel()`: 核心计算逻辑（拆分为多个私有方法）
- `createLogContent()`: 日志生成逻辑

#### 3. 设计缓存结构
```java
private static class CalculationCache {
    SysUser manager;
    List<SysUser> teamMembers;
    // 其他需要在calculateLevel和createLogContent间共享的数据
}
```

#### 4. 确定数据查询策略
- **纯等级聚合**：只查询 `tb_performance_indicator` 表
- **复合数据计算**：额外查询原始业务数据表和外部接口

#### 5. 实现业务规则
- 一票否决检查
- 等级聚合逻辑
- 异常情况处理

### 6.2 代码质量要求

#### ✅ 必须遵守
- **常量使用**：从 `CommConstants.CommonVal.*` 获取数字常量
- **方法拆分**：每个方法不超过50行，职责单一
- **缓存使用**：避免在两个方法中重复查询相同数据
- **异常处理**：合理的默认值和错误日志
- **详细日志**：记录计算过程和原因

#### ❌ 禁止事项
- **魔法值**：不要在代码中直接使用数字
- **长方法**：不要写超过80行的方法
- **重复查询**：不要在 `calculateLevel` 和 `createLogContent` 中重复查询
- **忽略异常**：不要忽略边界情况和异常处理

### 6.3 测试验证

#### 验证点
1. **数据正确性**：确保查询的数据准确完整
2. **等级计算**：验证各种场景下的等级计算结果
3. **边界情况**：无数据、异常数据等情况
4. **性能表现**：避免N+1查询问题
5. **日志完整性**：确保日志信息足够详细

## 7. 总结

### 7.1 核心原则
1. **分层计算**：技术经理指标基于团队成员的已计算等级
2. **数据复用**：使用ThreadLocal缓存避免重复查询和计算
3. **规则清晰**：每个指标都有明确的聚合规则
4. **异常处理**：合理的默认值和降级策略
5. **代码质量**：使用常量、拆分方法、详细日志

### 7.2 技术架构
- **数据层**：`tb_performance_indicator` 表存储基础等级数据
- **服务层**：`IndicatorLevelCalcService` 实现类处理聚合逻辑
- **工具层**：`ScoreLevelUtil` 提供等级合并能力
- **常量层**：`CommConstants.CommonVal` 提供数字常量

### 7.3 两种计算模式
1. **纯等级聚合模式**（推荐）：
   - 只使用 `tb_performance_indicator` 表的等级数据
   - 计算逻辑简单清晰
   - 性能较好
   - 适用场景：基于团队成员专业表现的管理指标

2. **复合数据计算模式**：
   - 结合等级数据、原始数据、外部接口数据
   - 计算逻辑复杂
   - 性能相对较低
   - 适用场景：需要综合多种数据源的复杂管理指标

### 7.4 扩展方向
随着业务发展，可能需要支持：
- 更复杂的角色映射关系
- 动态的权重配置
- 跨部门的团队管理指标
- 历史趋势分析
- 自定义评级规则 