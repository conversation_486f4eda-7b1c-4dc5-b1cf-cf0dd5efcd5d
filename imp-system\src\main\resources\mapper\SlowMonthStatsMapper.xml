<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.SlowMonthStatsMapper">

    <resultMap type="com.qmqb.imp.system.domain.SlowMonthStats" id="SlowMonthStatsResult">
        <result property="id" column="id"/>
        <result property="month" column="month"/>
        <result property="dbName" column="DBName"/>
        <result property="sqlHash" column="SQLHash"/>
        <result property="assigner" column="assigner"/>
        <result property="assignTime" column="assign_time"/>
        <result property="processer" column="processer"/>
        <result property="processTime" column="process_time"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="analysisResult" column="analysis_result"/>
        <result property="createTime" column="create_time"/>
        <result property="assignerId" column="assigner_id"/>
        <result property="processerId" column="processer_id"/>
    </resultMap>
    <select id="slowSqlPage" resultType="com.qmqb.imp.system.domain.vo.SlowMonthStatsVo">
        select
        sms.id id,
        sqi.IP ip,
        sqi.last_executionStartTime last_execution_start_time,
        sqi.SQLHash sqlHash,
        sqi.total_query_times totalQueryTimes,
        sqi.total_sum_time totalSumTime,
        sqi.avg_query_time avgQueryTime,
        sqi.slow_rule slowRule,
        sqi.warn_level warnLevel,
        sms.status status,
        sms.process_time processTime,
        sms.processer processer,
        sms.remark remark,
        sms.analysis_result analysisResult,
        sms.assigner assigner,
        sms.assign_time assignTime,
        sms.assigner_id assignerId,
        sms.processer_id processerId,
        sms.DBName dbName,
        sqdl.db_name dbRemark
        from t_slow_month_stats sms
        left join t_slow_query_info sqi on sms.SQLHash = sqi.SQLHash
        left join t_slow_query_db_list sqdl on sms.DBName = sqdl.db_code
        <where>
            <if test="bo.dbName != null and bo.dbName !=''">
                and sms.DBName = #{bo.dbName}
            </if>
            <if test="bo.ip != null and bo.ip !=''">
                and sqi.IP = #{bo.ip}
            </if>
            <if test="bo.warnLevel != null and bo.warnLevel !=''">
                and sqi.warn_level = #{bo.warnLevel}
            </if>
            <if test="bo.slowRule != null and bo.slowRule !=''">
                and sqi.slow_rule = #{bo.slowRule}
            </if>
            <if test="bo.status != null and bo.status != '-1'">
                and sms.status = #{bo.status}
            </if>
            <if test="bo.processer != null and bo.processer != ''">
                and sms.processer = #{bo.processer}
            </if>
            <if test="bo.assigner != null and bo.assigner != ''">
                and sms.assigner = #{bo.assigner}
            </if>
            <if test="bo.processTimeStart != null">
                and sms.process_time >= #{bo.processTimeStart}
            </if>
            <if test="bo.processTimeEnd != null">
                and sms.process_time &lt;= #{bo.processTimeEnd}
            </if>
            <if test="bo.assignTimeStart != null">
                and sms.assign_time >= #{bo.assignTimeStart}
            </if>
            <if test="bo.assignTimeEnd != null">
                and sms.assign_time &lt;= #{bo.assignTimeEnd}
            </if>
            <if test="bo.idList != null and bo.idList.size() > 0">
                and sms.id in <foreach collection="bo.idList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="bo.dbList != null and bo.dbList.size() > 0">
                and sms.DBName in <foreach collection="bo.dbList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="bo.processerId != null">
                and sms.processer_id = #{bo.processerId}
            </if>
            <if test="bo.year != null and bo.month != null">
                and sms.month = concat(#{bo.year},lpad(#{bo.month}, 2, '0'))
            </if>
            <if test="bo.year != null and bo.month == null">
                and sms.month like concat(#{bo.year},'%')
            </if>
        </where>
    </select>


</mapper>
