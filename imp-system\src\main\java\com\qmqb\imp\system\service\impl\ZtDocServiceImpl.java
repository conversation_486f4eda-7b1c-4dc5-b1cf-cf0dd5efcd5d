package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.common.exception.ServiceException;
import com.hzed.structure.common.util.ObjectUtil;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.dto.CountByUserAndAddedDateDTO;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.DocLibType;
import com.qmqb.imp.common.utils.BeanCopyUtils;
import com.qmqb.imp.system.domain.ZtDoc;
import com.qmqb.imp.system.domain.bo.CountByUserAndAddedDateBO;
import com.qmqb.imp.system.domain.bo.WorkDetailBo;
import com.qmqb.imp.system.domain.bo.ZtDocBo;
import com.qmqb.imp.system.domain.dto.CountDocLibDTO;
import com.qmqb.imp.system.domain.dto.DocLibStatisticsDTO;
import com.qmqb.imp.system.domain.vo.DocCountVO;
import com.qmqb.imp.system.domain.vo.DocLibStatisticsVO;
import com.qmqb.imp.system.domain.vo.DocVO;
import com.qmqb.imp.system.domain.vo.ZtDocContentVo;
import com.qmqb.imp.system.domain.vo.ZtDocVo;
import com.qmqb.imp.system.mapper.ZtDocContentMapper;
import com.qmqb.imp.system.mapper.ZtDocMapper;
import com.qmqb.imp.system.service.DeptCacheService;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.IZtDocService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.qmqb.imp.common.utils.DateUtils.*;

/**
 * 禅道文档Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ZtDocServiceImpl implements IZtDocService {

    private final ZtDocMapper baseMapper;
    private final ZtDocContentMapper ztDocContentMapper;
    private final ISysUserService sysUserService;
    private final DeptCacheService deptCacheService;

    /**
     * 查询禅道文档
     */
    @Override
    public ZtDocVo queryById(Integer id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询禅道文档列表
     */
    @Override
    public TableDataInfo<ZtDocVo> queryPageList(ZtDocBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ZtDoc> lqw = buildQueryWrapper(bo);
        Page<ZtDocVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询禅道文档列表
     */
    @Override
    public List<ZtDocVo> queryList(ZtDocBo bo) {
        LambdaQueryWrapper<ZtDoc> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ZtDoc> buildQueryWrapper(ZtDocBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ZtDoc> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProject() != null, ZtDoc::getProject, bo.getProject());
        lqw.eq(bo.getProduct() != null, ZtDoc::getProduct, bo.getProduct());
        lqw.eq(bo.getExecution() != null, ZtDoc::getExecution, bo.getExecution());
        lqw.eq(StringUtils.isNotBlank(bo.getLib()), ZtDoc::getLib, bo.getLib());
        lqw.eq(StringUtils.isNotBlank(bo.getModule()), ZtDoc::getModule, bo.getModule());
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), ZtDoc::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getKeywords()), ZtDoc::getKeywords, bo.getKeywords());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), ZtDoc::getType, bo.getType());
        lqw.eq(bo.getParent() != null, ZtDoc::getParent, bo.getParent());
        lqw.eq(StringUtils.isNotBlank(bo.getPath()), ZtDoc::getPath, bo.getPath());
        lqw.eq(bo.getGrade() != null, ZtDoc::getGrade, bo.getGrade());
        lqw.eq(bo.getOrder() != null, ZtDoc::getOrder, bo.getOrder());
        lqw.eq(bo.getViews() != null, ZtDoc::getViews, bo.getViews());
        lqw.eq(StringUtils.isNotBlank(bo.getDraft()), ZtDoc::getDraft, bo.getDraft());
        lqw.eq(StringUtils.isNotBlank(bo.getAddedBy()), ZtDoc::getAddedBy, bo.getAddedBy());
        lqw.eq(bo.getAddedDate() != null, ZtDoc::getAddedDate, bo.getAddedDate());
        lqw.eq(StringUtils.isNotBlank(bo.getEditedBy()), ZtDoc::getEditedBy, bo.getEditedBy());
        lqw.eq(bo.getEditedDate() != null, ZtDoc::getEditedDate, bo.getEditedDate());
        lqw.eq(StringUtils.isNotBlank(bo.getMailto()), ZtDoc::getMailto, bo.getMailto());
        lqw.eq(StringUtils.isNotBlank(bo.getAcl()), ZtDoc::getAcl, bo.getAcl());
        lqw.eq(StringUtils.isNotBlank(bo.getGroups()), ZtDoc::getGroups, bo.getGroups());
        lqw.eq(StringUtils.isNotBlank(bo.getUsers()), ZtDoc::getUsers, bo.getUsers());
        lqw.eq(StringUtils.isNotBlank(bo.getDeleted()), ZtDoc::getDeleted, bo.getDeleted());
        return lqw;
    }

    /**
     * 新增禅道文档
     */
    @Override
    public Boolean insertByBo(ZtDocBo bo) {
        ZtDoc add = BeanUtil.toBean(bo, ZtDoc.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改禅道文档
     */
    @Override
    public Boolean updateByBo(ZtDocBo bo) {
        ZtDoc update = BeanUtil.toBean(bo, ZtDoc.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ZtDoc entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除禅道文档
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Integer> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @DS(DataSource.ZENTAO)
    public List<DocCountVO> selectDocCountVOList(Integer doneYear, Integer doneMonth)  {
        Date beginTime = dateToStart(yyMMDDtoDate(getFirstDayOfMonth(doneYear, doneMonth)));
        Date endTime = dateToEnd(yyMMDDtoDate(getLastDayOfMonth(doneYear, doneMonth)));
        List<SysUser> sysUsers = sysUserService.selectAllUser();
        List<String> nameList = sysUsers.stream().map(SysUser::getZtUserName).collect(Collectors.toList());
        List<DocCountVO> docCountVos = this.baseMapper.selectDocCountVOList(nameList, beginTime, endTime);
        docCountVos.forEach(docCountVO -> docCountVO.setUsername(sysUsers.stream().filter(sysUser -> StringUtils.equals(sysUser.getZtUserName(), docCountVO.getUsername())).map(SysUser::getNickName).findAny().orElse("")));
        return docCountVos;
    }

    @Override
    @DS(DataSource.ZENTAO)
    public List<DocCountVO> selectDocCountVoListEngName(List<SysUser> userList, Date beginTime, Date endTime) {
        List<String> nameList = userList.stream().map(SysUser::getZtUserName).collect(Collectors.toList());
        return this.baseMapper.selectDocCountVOList(nameList, beginTime, endTime);
    }

    @Override
    @DS(DataSource.ZENTAO)
    public List<DocVO> getDocListByGroup(Integer doneYear, Integer doneMonth, Long groupId) {
        Date beginTime = dateToStart(yyMMDDtoDate(getFirstDayOfMonth(doneYear, doneMonth)));
        Date endTime = dateToEnd(yyMMDDtoDate(getLastDayOfMonth(doneYear, doneMonth)));
        if (ObjectUtil.isNull(groupId)) {
            return new ArrayList<>();
        }
        // 获取技术中心所有用户列表
        List<SysUser> sysUsers = sysUserService.selectAllUser();
        Map<Long, List<SysUser>> sysUserMap = sysUsers.stream().collect(Collectors.groupingBy(e -> e.getDept().getDeptId()));
        List<SysUser> deptUsers = sysUserMap.get(groupId);
        //该组无用户
        if (CollectionUtil.isEmpty(deptUsers)) {
            return new ArrayList<>();
        }
        List<String> memberList = deptUsers.stream().map(SysUser::getZtUserName).collect(Collectors.toList());

        List<DocVO> docVOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(memberList)) {
            docVOList = this.baseMapper.getDocListByGroup(memberList, beginTime, endTime);
            docVOList.forEach(docVO -> {
                String groupName = deptCacheService.getDeptNameById(groupId);
                docVO.setGroupName(groupName);
                String chiName = sysUsers.stream().filter(sysUser -> StringUtils.equals(sysUser.getZtUserName(), docVO.getAddedBy())).map(SysUser::getNickName).findAny().orElse("");
                docVO.setAddedBy(chiName);
                if (Objects.equals(CommConstants.CommonVal.ZERO, docVO.getProduct()) && Objects.equals(CommConstants.CommonVal.ZERO, docVO.getExecution())) {
                    docVO.setJumpUrl("http://pm.qmqb.top/doc-view-" + docVO.getId() + ".html");
                } else if (!Objects.equals(CommConstants.CommonVal.ZERO, docVO.getProduct())) {
                    docVO.setJumpUrl("http://pm.qmqb.top/doc-view-" + docVO.getId() + ".html");
                } else if (!Objects.equals(CommConstants.CommonVal.ZERO, docVO.getExecution())) {
                    docVO.setJumpUrl("http://pm.qmqb.top/doc-view-"  + docVO.getId() + ".html");
                }
            });
        }
        return docVOList;
    }

    @Override
    public List<ZtDocVo> getUserDocuments(String userName , String beginTime, String endTime) {
        return this.baseMapper.getUserDocuments(userName,beginTime,endTime);
    }

    @Override
    @DS(DataSource.ZENTAO)
    public List<ZtDocContentVo> getUserDocumentsWithContent(String userName, String beginTime, String endTime) {
        return ztDocContentMapper.getUserDocumentsWithContent(userName, beginTime, endTime);
    }

    @Override
    @DS(DataSource.ZENTAO)
    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true,rollbackFor = Exception.class)
    public List<CountByUserAndAddedDateDTO> countByUserAndAddedDate(List<CountByUserAndAddedDateBO> queryDocList, Integer month) {
        return baseMapper.countByUserAndAddedDate(queryDocList, month);
    }

    /**
     * 统计文档库文档数量
     * @return
     */
    @Override
    public TableDataInfo<DocLibStatisticsVO> getDocLibStatistics(PageQuery pageQuery) {
        List<DocLibStatisticsVO> retVos = new ArrayList<>();

        // 自定义库统计汇总
        List<DocLibStatisticsDTO> customDocLibStats = baseMapper.getDocLibInfoByType(DocLibType.CUSTOM.getType());
        List<Integer> customLibIds = customDocLibStats.stream().map(DocLibStatisticsDTO::getLibId).collect(Collectors.toList());
        Map<Integer, CountDocLibDTO> customLibCountMap = baseMapper.countDocLib(customLibIds).stream().collect(Collectors.toMap(CountDocLibDTO::getLibId, Function.identity()));
        // 组装项目库的链接和数量信息
        for (DocLibStatisticsDTO customDocLibStat : customDocLibStats) {
            DocLibStatisticsVO copy = new DocLibStatisticsVO();
            BeanCopyUtils.copy(customDocLibStat, copy);
            CountDocLibDTO countDocLibDTO = customLibCountMap.get(customDocLibStat.getLibId());
            copy.setDocumentCount(ObjectUtil.isNull(countDocLibDTO) ? 0 : countDocLibDTO.getDocumentCount());
            copy.setBelongToLib("自定义库");
            copy.setLink("https://pm.qmqb.top/doc-teamspace-" + customDocLibStat.getParent() + "-"  + customDocLibStat.getLibId() + ".html");
            retVos.add(copy);
        }

        // 产品库统计汇总
        List<DocLibStatisticsDTO> productDocLibStats = baseMapper.getProductDocLibStat();
        List<Integer> productLibIds = productDocLibStats.stream().map(DocLibStatisticsDTO::getLibId).collect(Collectors.toList());
        Map<Integer, CountDocLibDTO> productLibCountMap = baseMapper.countDocLib(productLibIds).stream().collect(Collectors.toMap(CountDocLibDTO::getLibId, Function.identity()));
        // 组装产品库的链接和数量信息
        for (DocLibStatisticsDTO productDocLibStat : productDocLibStats) {
            DocLibStatisticsVO copy = new DocLibStatisticsVO();
            BeanCopyUtils.copy(productDocLibStat, copy);
            CountDocLibDTO countDocLibDTO = productLibCountMap.get(productDocLibStat.getLibId());
            copy.setDocumentCount(ObjectUtil.isNull(countDocLibDTO) ? 0 : countDocLibDTO.getDocumentCount());
            copy.setLink("https://pm.qmqb.top/doc-view-" + productDocLibStat.getProductId() +"-" + productDocLibStat.getLibId() + ".html");
            retVos.add(copy);
        }

        // 项目库统计汇总
        List<DocLibStatisticsDTO> projectDocLibStats = baseMapper.getProjectDocLibStat();
        List<Integer> projectLibIds = projectDocLibStats.stream().map(DocLibStatisticsDTO::getLibId).collect(Collectors.toList());
        Map<Integer, CountDocLibDTO> projectLibCountMap = baseMapper.countDocLib(projectLibIds).stream().collect(Collectors.toMap(CountDocLibDTO::getLibId, Function.identity()));
        // 组装项目库的链接和数量信息
        for (DocLibStatisticsDTO projectDocLibStat : projectDocLibStats) {
            DocLibStatisticsVO copy = new DocLibStatisticsVO();
            BeanCopyUtils.copy(projectDocLibStat, copy);
            CountDocLibDTO countDocLibDTO = projectLibCountMap.get(projectDocLibStat.getLibId());
            copy.setDocumentCount(ObjectUtil.isNull(countDocLibDTO) ? 0 : countDocLibDTO.getDocumentCount());
            copy.setLink("https://pm.qmqb.top/execution-task-" + projectDocLibStat.getProjectId() +".html");
            retVos.add(copy);
        }

        // 接口库统计汇总
        List<DocLibStatisticsDTO> apiDocLibStats = baseMapper.getDocLibInfoByType(DocLibType.API.getType());
        List<Integer> apiLibIds = apiDocLibStats.stream().map(DocLibStatisticsDTO::getLibId).collect(Collectors.toList());
        Map<Integer, CountDocLibDTO> apiLibCountMap = baseMapper.countApiDocLib(apiLibIds).stream().collect(Collectors.toMap(CountDocLibDTO::getLibId, Function.identity()));
        // 组装接口文档库的链接和数量信息
        for (DocLibStatisticsDTO apiDocLibStat : apiDocLibStats) {
            DocLibStatisticsVO copy = new DocLibStatisticsVO();
            BeanCopyUtils.copy(apiDocLibStat, copy);
            CountDocLibDTO countDocLibDTO = apiLibCountMap.get(apiDocLibStat.getLibId());
            copy.setDocumentCount(ObjectUtil.isNull(countDocLibDTO) ? 0 : countDocLibDTO.getDocumentCount());
            copy.setBelongToLib("接口库");
            copy.setLink("https://pm.qmqb.top/api-index-" + apiDocLibStat.getLibId() + ".html");
            retVos.add(copy);
        }

        // 手动分页
        Integer pageNum = pageQuery.getPageNum();
        Integer pageSize = pageQuery.getPageSize();
        Integer start = (pageNum - 1) * pageSize;
        int total = retVos.size();
        if (start > total) {
            return new TableDataInfo<>(ListUtil.empty(), total);
        } else if (start + pageSize >= total) {
            return new TableDataInfo<>(retVos.subList(start, total), total);
        }
        return new TableDataInfo<>(retVos.subList(start, start + pageSize), total);
    }

    @Override
    @DS(DataSource.ZENTAO)
    public Long countByAddedByAndAddedDate(Collection<String> addedBys, Date beginTime, Date endTime) {
        return this.baseMapper.selectCount(Wrappers.lambdaQuery(ZtDoc.class).in(ZtDoc::getAddedBy, addedBys).between(ZtDoc::getAddedDate, beginTime, endTime));
    }

    @Override
    public List<ZtDoc> listByAddedByAndAddedDate(Collection<String> addedBys, Date beginTime, Date endTime) {
        return this.baseMapper.selectList(Wrappers.lambdaQuery(ZtDoc.class).in(ZtDoc::getAddedBy, addedBys).between(ZtDoc::getAddedDate, beginTime, endTime));
    }

    @Override
    public Long selectDocCountByAddedDate(Date beginTime, Date endTime) {
        return this.baseMapper.selectCount(Wrappers.lambdaQuery(ZtDoc.class).between(ZtDoc::getAddedDate, beginTime, endTime).eq(ZtDoc::getDeleted, CommConstants.CommonValStr.ZERO));
    }

    @Override
    @DS(DataSource.ZENTAO)
    public Page<DocVO> getUserDocList(WorkDetailBo workDetail) {
        Page<DocVO> page = new Page<>(workDetail.getPageNo(), workDetail.getPageSize());
        Integer startMonth;
        Integer endMonth;
        if (ObjectUtils.isEmpty(workDetail.getEvalMonth())) {
            startMonth = CommConstants.CommonVal.ONE;
            endMonth = CommConstants.CommonVal.TWELVE;
        }else {
            startMonth = workDetail.getEvalMonth();
            endMonth = workDetail.getEvalMonth();
        }
        Date beginTime = dateToStart(yyMMDDtoDate(getFirstDayOfMonth(workDetail.getEvalYear(), startMonth)));
        Date endTime = dateToEnd(yyMMDDtoDate(getLastDayOfMonth(workDetail.getEvalYear(), endMonth)));
        List<SysUser> sysUsers = sysUserService.selectUserByNickNames(Collections.singleton(workDetail.getWorkUsername()));
        if (CollUtil.isEmpty(sysUsers)) {
            throw new ServiceException("该用户不存在");
        }
        workDetail.setZtUserName(sysUsers.get(0).getZtUserName());

        Page<DocVO> docVOList = this.baseMapper.getUserDocList(page, workDetail, beginTime, endTime);

        docVOList.getRecords().forEach(docVO -> {
            String groupName = deptCacheService.getDeptNameById(sysUsers.get(0).getDeptId());
            docVO.setGroupName(groupName);
            String chiName = sysUsers.stream().filter(sysUser -> StringUtils.equals(sysUser.getZtUserName(), docVO.getAddedBy())).map(SysUser::getNickName).findAny().orElse("");
            docVO.setAddedBy(chiName);
            if (Objects.equals(CommConstants.CommonVal.ZERO, docVO.getProduct()) && Objects.equals(CommConstants.CommonVal.ZERO, docVO.getExecution())) {
                docVO.setJumpUrl("http://pm.qmqb.top/doc-view-" + docVO.getId() + ".html");
            } else if (!Objects.equals(CommConstants.CommonVal.ZERO, docVO.getProduct())) {
                docVO.setJumpUrl("http://pm.qmqb.top/doc-view-" + docVO.getId() + ".html");
            } else if (!Objects.equals(CommConstants.CommonVal.ZERO, docVO.getExecution())) {
                docVO.setJumpUrl("http://pm.qmqb.top/doc-view-"  + docVO.getId() + ".html");
            }
        });

        return docVOList;

    }
}
