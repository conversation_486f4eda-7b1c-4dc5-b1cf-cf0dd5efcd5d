package com.qmqb.imp.web.controller.openapi;

import com.qmqb.imp.system.domain.bo.TrackWorkResultBO;
import com.qmqb.imp.system.domain.vo.PerformanceDataVO;
import com.qmqb.imp.web.aop.IpWhiteListCheck;
import com.qmqb.imp.system.service.ITrackWorkResultService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @className: OpenapiController
 * @author: yangjiangqian
 * @description:
 * @date: 2025/6/18 10:57
 * @version: 1.0
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/openapi")
public class OpenapiController {

    private final ITrackWorkResultService trackWorkResultService;



    @IpWhiteListCheck
    @GetMapping("/trackWorkResult/list")
    public PerformanceDataVO list(TrackWorkResultBO trackWorkResultBO, HttpServletRequest request) {
        trackWorkResultBO.setPageNum(1);
        trackWorkResultBO.setPageSize(1000);
        return trackWorkResultService.listOpenApi(trackWorkResultBO);
    }

}
