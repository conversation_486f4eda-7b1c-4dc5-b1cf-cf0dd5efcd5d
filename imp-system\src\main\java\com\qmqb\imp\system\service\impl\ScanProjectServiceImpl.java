package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.dto.CodeBlockerStatisticsDTO;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.system.domain.ScanProject;
import com.qmqb.imp.system.domain.bo.ScanProjectBo;
import com.qmqb.imp.system.domain.vo.ScanProjectVo;
import com.qmqb.imp.system.mapper.ScanProjectMapper;
import com.qmqb.imp.system.service.IScanProjectService;
import com.qmqb.imp.system.service.ISysDeptService;
import com.qmqb.imp.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.*;

/**
 * 扫描项目记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@RequiredArgsConstructor
@Service
public class ScanProjectServiceImpl implements IScanProjectService {

    private final ScanProjectMapper baseMapper;
    private final ISysUserService sysUserService;
    private final ISysDeptService sysDeptService;

    /**
     * 查询扫描项目记录
     */
    @Override
    public ScanProjectVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询扫描项目记录列表
     */
    @Override
    public TableDataInfo<ScanProjectVo> queryPageList(ScanProjectBo bo, PageQuery pageQuery) {
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn(bo.getOrderByField());
        }
        if (StringUtils.isBlank(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc(bo.getOrderRule());
        }
        // 权限控制逻辑
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("当前用户未登录");
        }
        SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());
        // 权限过滤：如果不是管理员或技术总监或项目经理，只能查看自己所在组的数据
        boolean isLimitedToOwnGroup = false;
        if (currentUser != null && currentUser.getRoles() != null) {
            if (!currentUser.isAdmin() && !currentUser.isJszxAdmin() && !currentUser.isProjectManager()) {
                isLimitedToOwnGroup = true;
            }
        }
        List<Long> groupIdList=new ArrayList<>();
        if (isLimitedToOwnGroup) {
            // 仅能查看自己组的数据
            groupIdList = Collections.singletonList(currentUser.getDeptId());
        } else {
            //可以看全部组的数据，0表示全部数据，1表示其他组id
            try {
                if (StrUtil.isNotBlank(bo.getDevDept()) && !CommConstants.CommonValStr.ZERO.equals(bo.getDevDept())) {
                    Long deptId = Long.parseLong(bo.getDevDept());
                    groupIdList = Collections.singletonList(deptId);
                }
            } catch (NumberFormatException e) {
                // 如果无法解析为Long，则使用默认的技术中心部门列表
                groupIdList = sysDeptService.listTecCenterDeptIdList();
            }
        }
        // 设置部门权限过滤条件
        bo.setGroupIdList(groupIdList);
        Page<ScanProjectVo> result = baseMapper.page(pageQuery.build(), bo);
        return TableDataInfo.build(result);
    }

    /**
     * 查询扫描项目记录列表
     */
    @Override
    public List<ScanProjectVo> queryList(ScanProjectBo bo) {
        LambdaQueryWrapper<ScanProject> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ScanProject> buildQueryWrapper(ScanProjectBo bo) {
        LambdaQueryWrapper<ScanProject> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getScanName()), ScanProject::getScanName, bo.getScanName());
        lqw.eq(bo.getPId() != null, ScanProject::getPId, bo.getPId());
        lqw.eq(StringUtils.isNotBlank(bo.getScanNamespace()), ScanProject::getScanNamespace, bo.getScanNamespace());
        lqw.eq(StringUtils.isNotBlank(bo.getScanDescribe()), ScanProject::getScanDescribe, bo.getScanDescribe());
        lqw.eq(StringUtils.isNotBlank(bo.getWebUrl()), ScanProject::getWebUrl, bo.getWebUrl());
        lqw.eq(bo.getLastCommitTime() != null, ScanProject::getLastCommitTime, bo.getLastCommitTime());
        lqw.eq(bo.getDevDept() != null && !Objects.equals(bo.getDevDept(), 0L), ScanProject::getDevDept, bo.getDevDept());
        lqw.eq(bo.getBlockerAmount() != null, ScanProject::getBlockerAmount, bo.getBlockerAmount());
        lqw.eq(bo.getCriticalAmount() != null, ScanProject::getCriticalAmount, bo.getCriticalAmount());
        lqw.eq(bo.getScanVersion() != null, ScanProject::getScanVersion, bo.getScanVersion());
        lqw.eq(bo.getLastScanFlag() != null, ScanProject::getLastScanFlag, bo.getLastScanFlag());
        return lqw;
    }

    /**
     * 新增扫描项目记录
     */
    @Override
    public Boolean insertByBo(ScanProjectBo bo) {
        ScanProject add = BeanUtil.toBean(bo, ScanProject.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 新增记录
     *
     * @param scanProject
     * @return
     */
    @Override
    public Boolean insert(ScanProject scanProject) {
        return baseMapper.insert(scanProject) > 0;
    }

    /**
     * 修改扫描项目记录
     */
    @Override
    public Boolean updateByBo(ScanProjectBo bo) {
        ScanProject update = BeanUtil.toBean(bo, ScanProject.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ScanProject entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除扫描项目记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 批量删除扫描项目记录
     *
     * @param deleteIds
     * @return
     */
    @Override
    public Boolean batchDeleteByIds(Collection<Long> deleteIds) {
        LambdaQueryWrapper<ScanProject> wrapper = Wrappers.lambdaQuery();
        wrapper.in(ScanProject::getPId, deleteIds);
        return baseMapper.delete(wrapper) > 0;
    }

    /**
     * 批量修改开发部门
     *
     * @param devDeptInPid
     */
    @Override
    public int updateDevDeptBatch(Map<Long, String> devDeptInPid) {
        return baseMapper.batchUpdateDevDept(devDeptInPid);
    }

    @Override
    public int updatePreviousScanToOld(Long pid) {
        return baseMapper.updatePreviousScanToOld(pid);
    }

    @Override
    public Page<ScanProject> pageList(ScanProjectBo scanProjectBo, Page page) {
        return baseMapper.selectPage(page, new QueryWrapper<>());
    }

    @Override
    public List<CodeBlockerStatisticsDTO> blockerStatisticsList(Integer year, Integer month) {
        LocalDateTime endTime = null;
        if (year != null && month != null) {
            endTime = LocalDateTime.of(LocalDate.of(year, month, YearMonth.of(year,month).lengthOfMonth()),LocalTime.MAX);
        } else if (year != null) {
            endTime = LocalDateTime.of(LocalDate.of(year, 12, 31),LocalTime.MAX);
        }
        return baseMapper.blockerStatisticsList(endTime);
    }

}
