package com.qmqb.imp.system.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.dto.CountByUserAndAddedDateDTO;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.ZtDoc;
import com.qmqb.imp.system.domain.bo.CountByUserAndAddedDateBO;
import com.qmqb.imp.system.domain.bo.WorkDetailBo;
import com.qmqb.imp.system.domain.bo.ZtDocBo;
import com.qmqb.imp.system.domain.vo.DocCountVO;
import com.qmqb.imp.system.domain.vo.DocLibStatisticsVO;
import com.qmqb.imp.system.domain.vo.DocVO;
import com.qmqb.imp.system.domain.vo.ZtDocContentVo;
import com.qmqb.imp.system.domain.vo.ZtDocVo;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 禅道文档Service接口
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@DS(DataSource.ZENTAO)
public interface IZtDocService {

    /**
     * 查询禅道文档
     * @param id
     * @return
     */
    ZtDocVo queryById(Integer id);

    /**
     * 查询禅道文档列表
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<ZtDocVo> queryPageList(ZtDocBo bo, PageQuery pageQuery);

    /**
     * 查询禅道文档列表
     * @param bo
     * @return
     */
    List<ZtDocVo> queryList(ZtDocBo bo);

    /**
     * 新增禅道文档
     * @param bo
     * @return
     */
    Boolean insertByBo(ZtDocBo bo);

    /**
     * 修改禅道文档
     * @param bo
     * @return
     */
    Boolean updateByBo(ZtDocBo bo);

    /**
     * 校验并批量删除禅道文档信息
     * @param ids
     * @param isValid
     * @return
     */
    Boolean deleteWithValidByIds(Collection<Integer> ids, Boolean isValid);

    /**
     * 根据年份和月份查询文档数量
     * @param doneYear
     * @param doneMonth
     * @return
     */
    List<DocCountVO> selectDocCountVOList(Integer doneYear, Integer doneMonth);

    /**
     * 根据用户列表和添加时间查询文档数量
     * @param userList
     * @param beginTime
     * @param endTime
     * @return
     */
    List<DocCountVO> selectDocCountVoListEngName(List<SysUser> userList, Date beginTime, Date endTime);

    /**
     * 根据分组查询文档记录
     * @param doneYear
     * @param doneMonth
     * @param groupId
     * @return
     */
    List<DocVO> getDocListByGroup(Integer doneYear, Integer doneMonth, Long groupId);

    /**
     * 根据时间范围获取文档记录
     * @param userName
     * @param beginTime
     * @param endTime
     * @return
     */
    List<ZtDocVo> getUserDocuments(String userName,String beginTime, String endTime);

    /**
     * 根据时间范围获取文档记录（包含内容，兼容新版本数据）
     * @param userName
     * @param beginTime
     * @param endTime
     * @return
     */
    List<ZtDocContentVo> getUserDocumentsWithContent(String userName, String beginTime, String endTime);

    /**
     * 根据用户和添加时间统计文档数
     * @param queryDocList
     * @param month
     * @return
     */
    List<CountByUserAndAddedDateDTO> countByUserAndAddedDate(List<CountByUserAndAddedDateBO> queryDocList, Integer month);

    /**
     * 统计文档库文档数量
     * @param pageQuery
     * @return
     */
    TableDataInfo<DocLibStatisticsVO> getDocLibStatistics(PageQuery pageQuery);

    /**
     * 根据用户和添加时间统计文档数量
     * @param addedBys
     * @param beginTime
     * @param endTime
     * @return
     */
    Long countByAddedByAndAddedDate(Collection<String> addedBys, Date beginTime, Date endTime);

    /**
     * 根据用户和添加时间查询文档列表
     * @param addedBys
     * @param beginTime
     * @param endTime
     * @return
     */
    List<ZtDoc> listByAddedByAndAddedDate(Collection<String> addedBys, Date beginTime, Date endTime);

    /**
     * 查询指定时间段内的文档数量
     * @param beginTime
     * @param endTime
     * @return
     */
    Long selectDocCountByAddedDate(Date beginTime, Date endTime);

    /**
     * 获取个人文档记录
     * @param workDetail
     * @return
     */
    Page<DocVO> getUserDocList(WorkDetailBo workDetail);
}
