<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.performance.PerformanceTutoringMapper">

    <resultMap type="com.qmqb.imp.system.domain.performance.PerformanceTutoring" id="PerformanceTutoringResult">
        <result property="id" column="id"/>
        <result property="feedbackId" column="feedback_id"/>
        <result property="tutoringSummary" column="tutoring_summary"/>
        <result property="tutoringResult" column="tutoring_result"/>
        <result property="tutor" column="tutor"/>
        <result property="tutoringTime" column="tutoring_time"/>
        <result property="tutoringAttachment" column="tutoring_attachment"/>
        <result property="directorSuggest" column="director_suggest"/>
        <result property="suggestTime" column="suggest_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <select id="selectTutoringPage"
            resultType="com.qmqb.imp.system.domain.vo.performance.PerformanceTutoringVo">
        SELECT
        pt.*,pf.primary_indicator,pf.secondary_indicator,r.role_name,
        pf.event_title,pf.group_name,pf.nick_name,pf.recommended_level,
        pfm.feedback_code,pfm.event_start_time,pfm.event_end_time
        FROM tb_performance_tutoring pt
        LEFT JOIN tb_performance_feedback pf ON pt.feedback_id = pf.id
        LEFT JOIN tb_performance_feedback_main pfm on pf.main_feedback_id = pfm.id
        LEFT JOIN sys_role r on pf.person_type = r.role_id
        <where>
            <if test="bo.firstIndecator != null and bo.firstIndecator != ''">
                AND pf.primary_indicator = #{bo.firstIndecator}
            </if>
            <if test="bo.secondIndecator != null and bo.secondIndecator != ''">
                AND pf.secondary_indicator = #{bo.secondIndecator}
            </if>
            <if test="bo.level != null and bo.level != ''">
                AND pf.recommended_level = #{bo.level}
            </if>
            <if test="bo.nickName != null and bo.nickName != ''">
                AND pf.nick_name = #{bo.nickName}
            </if>
            <if test="bo.groupId != null">
                AND pf.group_id = #{bo.groupId}
            </if>
            <if test="bo.personType != null and bo.personType != 5 and bo.personType != 1">
                AND pf.person_type = #{bo.personType}
            </if>
            <if test="bo.personType == 5">AND pf.person_type IN ('3', '4', '7')</if>
        </where>
        ORDER BY pt.create_time DESC
    </select>


</mapper>
