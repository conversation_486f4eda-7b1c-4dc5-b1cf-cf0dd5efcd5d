<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.ZtDocContentMapper">

    <resultMap type="com.qmqb.imp.system.domain.ZtDocContent" id="ZtDocContentResult">
        <result property="id" column="id"/>
        <result property="doc" column="doc"/>
        <result property="title" column="title"/>
        <result property="digest" column="digest"/>
        <result property="content" column="content"/>
        <result property="html" column="html"/>
        <result property="rawContent" column="rawContent"/>
        <result property="files" column="files"/>
        <result property="type" column="type"/>
        <result property="addedBy" column="addedBy"/>
        <result property="addedDate" column="addedDate"/>
        <result property="editedBy" column="editedBy"/>
        <result property="editedDate" column="editedDate"/>
        <result property="version" column="version"/>
        <result property="fromVersion" column="fromVersion"/>
    </resultMap>

    <resultMap type="com.qmqb.imp.system.domain.vo.ZtDocContentVo" id="ZtDocContentVoResult">
        <result property="id" column="doc_id"/>
        <result property="doc" column="doc"/>
        <result property="title" column="title"/>
        <result property="digest" column="digest"/>
        <result property="content" column="content"/>
        <result property="html" column="html"/>
        <result property="rawContent" column="rawContent"/>
        <result property="files" column="files"/>
        <result property="type" column="type"/>
        <result property="addedBy" column="addedBy"/>
        <result property="addedDate" column="addedDate"/>
        <result property="editedBy" column="editedBy"/>
        <result property="editedDate" column="editedDate"/>
        <result property="version" column="version"/>
        <result property="fromVersion" column="fromVersion"/>
        <result property="project" column="project"/>
        <result property="product" column="product"/>
        <result property="execution" column="execution"/>
        <result property="lib" column="lib"/>
        <result property="draft" column="draft"/>
    </resultMap>

    <select id="getUserDocumentsWithContent" resultMap="ZtDocContentVoResult">
        SELECT 
            d.id as doc_id,
            d.project,
            d.product,
            d.execution,
            d.lib,
            d.title,
            d.addedBy,
            d.addedDate,
            d.draft,
            dc.content,
            dc.html,
            dc.rawContent,
            dc.digest,
            dc.files,
            dc.type,
            dc.version,
            dc.fromVersion,
            dc.editedBy,
            dc.editedDate
        FROM zt_doc d
        LEFT JOIN (
            SELECT 
                doc,
                content,
                html,
                rawContent,
                digest,
                files,
                type,
                version,
                fromVersion,
                editedBy,
                editedDate
            FROM zt_doccontent dc1
            WHERE dc1.version = (
                SELECT MAX(dc2.version) 
                FROM zt_doccontent dc2 
                WHERE dc2.doc = dc1.doc
            )
        ) dc ON d.id = dc.doc
        WHERE d.addedDate BETWEEN #{beginTime} AND #{endTime}
        AND d.addedBy = #{userName}
        AND d.deleted = '0'
        ORDER BY d.addedDate DESC
    </select>

</mapper> 