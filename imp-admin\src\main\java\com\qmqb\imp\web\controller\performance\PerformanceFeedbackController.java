package com.qmqb.imp.web.controller.performance;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackBo;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackVo;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 绩效反馈明细
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/performanceFeedback")
public class PerformanceFeedbackController extends BaseController {

    private final IPerformanceFeedbackService performanceFeedbackService;

    /**
     * 分页查询绩效反馈明细
     */
    @SaCheckPermission("system:performanceFeedback:list")
    @GetMapping("/list")
    public TableDataInfo<PerformanceFeedbackVo> list(PerformanceFeedbackBo bo, PageQuery pageQuery) {
        return performanceFeedbackService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询绩效反馈明细列表
     */
    @SaCheckPermission("system:performanceFeedback:query")
    @GetMapping("/all")
    public R<List<PerformanceFeedbackVo>> all(PerformanceFeedbackBo bo) {
        return R.ok(performanceFeedbackService.queryList(bo));
    }

    /**
     * 获取绩效反馈明细详细信息
     */
    @SaCheckPermission("system:performanceFeedback:query")
    @GetMapping("/{id}")
    public R<PerformanceFeedbackVo> getInfo(@NotNull(message = "主键不能为空")
                                            @PathVariable Long id) {
        return R.ok(performanceFeedbackService.queryById(id));
    }
}
