package com.qmqb.imp.system.service.impl.performance;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.entity.SysDictData;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.common.utils.ScoreLevelUtil;
import com.qmqb.imp.system.domain.bo.message.BaseMsgBo;
import com.qmqb.imp.system.domain.bo.message.EmailMsgBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceLevelBo;
import com.qmqb.imp.system.domain.dto.PerformanceDTO;
import com.qmqb.imp.system.domain.performance.Performance;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.performance.PerformanceIndicator;
import com.qmqb.imp.system.domain.performance.PerformanceIndicatorCategory;
import com.qmqb.imp.system.domain.vo.performance.PerformanceVo;
import com.qmqb.imp.system.mapper.performance.PerformanceMapper;
import com.qmqb.imp.system.service.ISysDictTypeService;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.indicator.IPerformanceIndicatorService;
import com.qmqb.imp.system.service.indicator.IPerformanceService;
import com.qmqb.imp.system.service.indicator.IPerformanceIndicatorCategoryService;
import com.qmqb.imp.system.service.message.IMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 绩效点评主Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PerformanceServiceImpl extends ServiceImpl<PerformanceMapper, Performance> implements IPerformanceService {

    private final PerformanceMapper baseMapper;

    @Autowired
    private IPerformanceIndicatorService indicatorService;

    @Autowired
    private IPerformanceIndicatorCategoryService indicatorCategoryService;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private IMessageService messageService;

    @Resource
    private ISysDictTypeService dictTypeService;
    /**
     * 查询绩效点评主
     */
    @Override
    public PerformanceVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询绩效点评主列表
     */
    @Override
    public TableDataInfo<PerformanceDTO> queryPageList(PerformanceBo bo, PageQuery pageQuery) {
        Page<PerformanceDTO> result = null;
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("当前用户未登录");
        }
        SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());

        // 权限过滤：如果不是管理员或技术总监或项目经理，只能查看自己所在组的数据
        boolean isLimitedToOwnGroup = false;
        if (currentUser != null && currentUser.getRoles() != null) {
            if (!currentUser.isAdmin() && !currentUser.isJszxAdmin() && !currentUser.isProjectManager()) {
                isLimitedToOwnGroup = true;
            }
        }
        if (isLimitedToOwnGroup) {
            bo.setGroupId(currentUser.getDeptId());
        }
        result = baseMapper.selectPerformanceSummary(pageQuery.build(), bo);
        return TableDataInfo.build(result);
    }

    /**
     * 查询绩效点评主列表
     */
    @Override
    public List<PerformanceVo> queryList(PerformanceBo bo) {
        LambdaQueryWrapper<Performance> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Performance> buildQueryWrapper(PerformanceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Performance> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getNickName()), Performance::getNickName, bo.getNickName());
        lqw.eq(bo.getYear() != null, Performance::getYear, bo.getYear());
        lqw.eq(bo.getMonth() != null, Performance::getMonth, bo.getMonth());
        lqw.eq(bo.getGroupId() != null, Performance::getGroupId, bo.getGroupId());
        lqw.like(StringUtils.isNotBlank(bo.getGroupName()), Performance::getGroupName, bo.getGroupName());
        lqw.eq(bo.getRoleId() != null, Performance::getRoleId, bo.getRoleId());
        lqw.eq(StringUtils.isNotBlank(bo.getRole()), Performance::getRole, bo.getRole());
        lqw.eq(StringUtils.isNotBlank(bo.getTotalLevel()), Performance::getTotalLevel, bo.getTotalLevel());
        lqw.eq(StringUtils.isNotBlank(bo.getReviewLevel()), Performance::getReviewLevel, bo.getReviewLevel());
        lqw.eq(StringUtils.isNotBlank(bo.getFinalLevel()), Performance::getFinalLevel, bo.getFinalLevel());
        lqw.eq(bo.getApprovalTime() != null, Performance::getApprovalTime, bo.getApprovalTime());
        lqw.eq(StringUtils.isNotBlank(bo.getEmailSentFlag()), Performance::getEmailSentFlag, bo.getEmailSentFlag());
        lqw.eq(bo.getEmailSentTime() != null, Performance::getEmailSentTime, bo.getEmailSentTime());
        return lqw;
    }

    /**
     * 新增绩效点评主
     */
    @Override
    public Boolean insertByBo(PerformanceBo bo) {
        Performance add = BeanUtil.toBean(bo, Performance.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改绩效点评主
     */
    @Override
    public Boolean updateByBo(PerformanceBo bo) {
        Performance update = BeanUtil.toBean(bo, Performance.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Performance entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除绩效点评主
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public void removeByNickNameAndMonth(String nickName, Integer month, Integer year) {

    }

    @Override
    public void removeByYearAndMonth(Integer year, Integer month, Integer personType, Long deptId) {
        String roleDesc = personType == 1 ? "组长" : "非组长";
        log.info("开始删除{}年{}月的{}绩效记录", year, month, roleDesc);

        // 1. 根据角色类型构建查询条件
        LambdaQueryWrapper<Performance> queryWrapper = new LambdaQueryWrapper<Performance>()
            .eq(Performance::getYear, year)
            .eq(Performance::getGroupId, deptId)
            .eq(Performance::getMonth, month);

        if (personType == 1) {
            // 组长：roleId = 2 (技术经理)
            queryWrapper.eq(Performance::getRoleId, 2L);
        } else {
            // 非组长：roleId != 2
            queryWrapper.ne(Performance::getRoleId, 2L);
        }

        // 2. 查询符合条件的绩效记录
        List<Performance> performances = this.baseMapper.selectList(queryWrapper);

        if (performances.isEmpty()) {
            log.info("{}年{}月的{}绩效记录为空，无需删除", year, month, roleDesc);
            return;
        }

        // 3. 获取绩效记录ID列表
        List<Long> performanceIds = performances.stream()
            .map(Performance::getId)
            .collect(Collectors.toList());

        log.info("找到{}条{}绩效记录，开始删除相关数据", performanceIds.size(), roleDesc);

        // 4. 删除相关的绩效指标记录
        indicatorService.remove(
            new LambdaQueryWrapper<PerformanceIndicator>()
                .in(PerformanceIndicator::getPerformanceId, performanceIds)
        );

        // 5. 删除相关的绩效指标分类记录
        indicatorCategoryService.remove(
            new LambdaQueryWrapper<PerformanceIndicatorCategory>()
                .in(PerformanceIndicatorCategory::getPerformanceId, performanceIds)
        );

        // 6. 删除绩效主表记录
        this.baseMapper.deleteBatchIds(performanceIds);

        log.info("删除{}年{}月的{}绩效记录完成，共删除{}条记录", year, month, roleDesc, performanceIds.size());
    }

    @Override
    public Performance getByNickNameAndMonth(String nickName, Integer year, Integer month) {
        return null;
    }

    @Override
    public List<Performance> listByYearAndMonth(Integer year, Integer month) {
        return Collections.emptyList();
    }

    /**
     * 计算总评等级（基于指标）
     * 规则：
     * 1. S：90%以上指标为S，且无C/D
     * 2. A：60%以上指标为S/A，且无D
     * 3. B：60%以上指标为A/B，且D不超过10%
     * 4. C：D不超过20%
     * 5. D：其他情况
     */
    @Override
    public String calculateTotalLevel(List<PerformanceIndicator> indicators) {
        return "";
    }

    /**
     * 基于分类计算总评等级
     * 规则：
     * 1. 统计各分类等级的数量
     * 2. 根据分类等级数量确定总评：
     * - 3个S：S级
     * - 2个S或3个A：A级
     * - 1个S或2个A或3个B：B级
     * - 其他情况：C级
     */
    @Override
    public String calculateTotalLevelByCategories(List<PerformanceIndicatorCategory> categories) {
        if (categories == null || categories.isEmpty()) {
            return ScoreLevelEnum.SCORE_C.getCode();
        }

        // 统计各等级数量
        Map<String, Long> levelCount = categories.stream()
            .collect(Collectors.groupingBy(PerformanceIndicatorCategory::getCategoryLevel, Collectors.counting()));

        double aCount = 0L;
        long cCount = levelCount.getOrDefault("C", 0L);

        for (PerformanceIndicatorCategory category : categories) {
            String level = category.getCategoryLevel();
            double aValue = ScoreLevelUtil.convertLevelToAvalue(level);
            aCount += aValue;
        }

        // 获取全部levels
        List<String> levels = categories.stream()
            .map(PerformanceIndicatorCategory::getCategoryLevel)
            .collect(Collectors.toList());

        return ScoreLevelUtil.determineCategoryLevel(aCount, (int) cCount,levels);
    }

    @Override
    public Boolean reviewLevel(PerformanceLevelBo performanceLevelBo) {
        if (CollectionUtil.isEmpty(performanceLevelBo.getIds())) {
            return false;
        }
        List<Performance> performances = baseMapper.selectList(new LambdaQueryWrapper<Performance>().in(Performance::getId, performanceLevelBo.getIds()));
        if (CollectionUtil.isEmpty(performances)) {
            return false;
        }
        performances.forEach(performance -> {
            performance.setReviewLevel(performanceLevelBo.getLevel());
        });

        return baseMapper.updateBatchById(performances);
    }

    @Override
    public Boolean approvalLevel(PerformanceLevelBo performanceLevelBo) {
        if (CollectionUtil.isEmpty(performanceLevelBo.getIds())) {
            return false;
        }
        List<Performance> performances = baseMapper.selectList(new LambdaQueryWrapper<Performance>().in(Performance::getId, performanceLevelBo.getIds()));
        if (CollectionUtil.isEmpty(performances)) {
            return false;
        }
        performances.forEach(performance -> {
            performance.setFinalLevel(performanceLevelBo.getLevel());
            performance.setApprovalTime(new Date());
        });

        return baseMapper.updateBatchById(performances);
    }

    @Override
    public Boolean sendEmail(List<Long> ids) {
        List<PerformanceVo> performanceVos = baseMapper.selectVoBatchIds(ids);
        if (CollectionUtil.isEmpty(performanceVos)) {
            throw new ServiceException("绩效点评id不能为空");
        }
        String names = performanceVos.stream().filter(performanceVo -> StringUtils.isBlank(performanceVo.getFinalLevel())).map(PerformanceVo::getNickName).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(names)) {
            throw new ServiceException("["+ names +"]最终绩效,不能为空");
        }
        // 收件人邮箱
        List<SysDictData> toDictList = dictTypeService.selectDictDataByType("performance_email_to_list");
        // 抄送人邮箱
        List<SysDictData> ccDictList = dictTypeService.selectDictDataByType("performance_email_cc_list");

        List<String> toList = toDictList.stream().map(SysDictData::getDictValue).map(String::trim).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(toList)) {
            throw new ServiceException("收件人邮箱不能为空");
        }
        List<String> ccList = ccDictList.stream().map(SysDictData::getDictValue).map(String::trim).collect(Collectors.toList());
        Integer month = performanceVos.get(0).getMonth();
        Integer year = performanceVos.get(0).getYear();
        String title = "技术中心"+ year +"年" + month +"月终评绩效";
        StringBuilder sb = new StringBuilder();
        sb.append("<html><body>")
            .append("<p>您好：</p>")
            .append("<p>经过评审，" + year + "年"+ month +"月技术中心的终评绩效如下表格所示：<br>")
            .append("<table border='1' cellpadding='5' cellspacing='0' style='border-collapse:collapse;'>")
            .append("<tr><th>所属组</th><th>岗位</th><th>姓名</th><th>本月核准绩效</th></tr>");

        for (PerformanceVo vo : performanceVos) {
            sb.append("<tr>")
                .append("<td>").append(vo.getGroupName() == null ? "" : vo.getGroupName()).append("</td>")
                .append("<td>").append(vo.getRole() == null ? "" : vo.getRole()).append("</td>")
                .append("<td>").append(vo.getNickName() == null ? "" : vo.getNickName()).append("</td>")
                .append("<td>").append(vo.getFinalLevel() == null ? "" : vo.getFinalLevel()).append("</td>")
                .append("</tr>");
        }

        sb.append("</table>")
            .append("</body></html>");

        String html = sb.toString();

        BaseMsgBo baseMsgBo = EmailMsgBo.builder().from("技术中心综合管理平台").text(html).subject(title).to(toList).cc(ccList).type("html").build();
        baseMsgBo.setChannelType(MessageChannelTypeEnum.EMAIL.getType());
        messageService.sendBase(baseMsgBo);
        baseMapper.updateBatchById(performanceVos.stream().map(performanceVo -> {
            Performance performance = new Performance();
            BeanUtil.copyProperties(performanceVo, performance);
            performance.setEmailSentTime(new Date());
            performance.setEmailSentFlag("1");
            return performance;
        }).collect(Collectors.toList()));
        return true;
    }
}
