package com.qmqb.imp.job.indicator;

import com.qmqb.imp.job.config.RoleIndicatorConfig;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 指标等级计算Service注册器
 *
 * <AUTHOR>
 */
@Component
public class IndicatorLevelCalcRegistry {
    private final List<IndicatorLevelCalcService> serviceList;
    private final Map<String, IndicatorLevelCalcService> serviceMap = new HashMap<>();

    @Autowired
    private RoleIndicatorConfig roleIndicatorConfig;

    @Autowired
    public IndicatorLevelCalcRegistry(List<IndicatorLevelCalcService> serviceList) {
        this.serviceList = serviceList;
    }

    @PostConstruct
    public void init() {
        for (IndicatorLevelCalcService service : serviceList) {
            serviceMap.put(service.getIndicatorCode(), service);
        }
    }

    /**
     * 根据指标编码获取对应Service
     */
    public IndicatorLevelCalcService getByCode(String code) {
        return serviceMap.get(code);
    }

    /**
     * 获取所有指标Service
     *
     * @deprecated 请使用 {@link #getAllByRole(PersonTypeEnum)} 替代
     */
    @Deprecated
    public List<IndicatorLevelCalcService> getAll() {
        return serviceList;
    }

    /**
     * 根据角色获取对应的指标Service列表
     *
     * @param roleCode 角色编码
     * @return 该角色对应的指标Service列表
     */
    public List<IndicatorLevelCalcService> getAllByRole(PersonTypeEnum personTypeEnum) {
        // 获取角色对应的指标编码列表
        List<String> indicatorCodes = roleIndicatorConfig.getIndicatorsByRole(personTypeEnum);

        // 根据指标编码获取对应的Service
        return indicatorCodes.stream()
            .map(this::getByCode)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 检查指标是否属于指定角色
     *
     * @param roleCode      角色编码
     * @param indicatorCode 指标编码
     * @return 是否属于
     */
    public boolean isIndicatorBelongToRole(PersonTypeEnum personTypeEnum, String indicatorCode) {
        List<String> indicatorCodes = roleIndicatorConfig.getIndicatorsByRole(personTypeEnum);
        return indicatorCodes.contains(indicatorCode);
    }
}
