package com.qmqb.imp.job.indicator.indicators.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.UserKqStat;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.mapper.UserKqStatMapper;
import com.qmqb.imp.system.service.ISysUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-07-03 16:28
 * 考勤指标统计
 */

@Service
public class AttendanceIndicatorServiceImpl implements IndicatorLevelCalcService {

    /**
     * S级标准：至少8小时，部门排名前10%,无早退，月迟到次数<=2
     */
    private static final double S_TOTAL_WORK_TIME = 8.0;
    private static final double S_LEVEL_PERCENTILE = 0.1;
    private static final int S_LATE_TIME = 2;
    private static final int S_LEAVE_EARLY = 0;

    /**
     * A级标准：至少8小时，部门排名前20%,无早退，月迟到次数<=5
     */
    private static final double A_TOTAL_WORK_TIME = 8.0;
    private static final double A_LEVEL_PERCENTILE = 0.2;
    private static final int A_LATE_TIME = 5;
    private static final int A_LEAVE_EARLY = 0;

    /**
     * B级标准：至少7小时且无早退,月迟到次数<=10
     */
    private static final double B_TOTAL_WORK_TIME = 7.0;
    private static final int B_LATE_TIME = 10;
    private static final int B_LEAVE_EARLY = 0;

    /**
     * C级标准：有>=3次早退或无故缺勤情况或月迟到次数>=11次
     */
    private static final int C_LATE_TIME = 11;
    private static final int C_LEAVE_EARLY = 3;
    private static final int C_ABSENT_TIME = 0;

    /**
     * D级标准：考勤指标有>=5次早退或无故缺勤情况,月迟到次数>=18次
     */
    private static final int D_LATE_TIME = 18;
    private static final int D_LEAVE_EARLY = 5;
    private static final int D_ABSENT_TIME = 0;

    @Resource
    UserKqStatMapper userKqStatMapper;
    @Resource
    ISysUserService sysUserService;


    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.ATTENDANCE.getCode();
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        // 1. 获取部门所有人员考勤数据用于排名计算(除组长外,全月请假的成员）
        List<UserKqStat> deptStats = getAllDeptStat(workResult);
        // 2. 获取当前用户考勤数据
        UserKqStat userStat = deptStats.stream().filter(stat -> stat.getKqUserName().equals(nickName)).findFirst().orElse(null);
        if (userStat == null) {
            // 没有数据直接跳过；
            return ScoreLevelEnum.SCORE_B.getCode();

        }
        // 3. 计算各项指标
        double dailyWorkHours = userStat.getKqAttendanceWorkTime() / 60.0 / userStat.getKqAttendanceDays();
        int lateCount = Optional.ofNullable(userStat.getKqLateCount()).orElse(0L).intValue();
        int earlyLeaveCount = Optional.ofNullable(userStat.getKqLeaveEarlyCount()).orElse(0L).intValue();
        int absentCount = Optional.ofNullable(userStat.getKqAbsenteeismDays()).orElse(0L).intValue();
        // 4. 计算部门工作时长百分位排名
        double percentileRank = calculatePercentileRank(deptStats, userStat);

        // 5. 评级逻辑
        Boolean sLevelFlag = dailyWorkHours >= S_TOTAL_WORK_TIME && percentileRank <= S_LEVEL_PERCENTILE &&
            lateCount <= S_LATE_TIME && earlyLeaveCount == S_LEAVE_EARLY;
        Boolean aLevelFlag = dailyWorkHours >= A_TOTAL_WORK_TIME && percentileRank <= A_LEVEL_PERCENTILE &&
                    lateCount <= A_LATE_TIME && earlyLeaveCount == A_LEAVE_EARLY;
        Boolean bLevelFlag = dailyWorkHours >= B_TOTAL_WORK_TIME && earlyLeaveCount == B_LEAVE_EARLY && lateCount <= B_LATE_TIME;
        Boolean cLevelFlag = earlyLeaveCount >= C_LEAVE_EARLY || absentCount > C_ABSENT_TIME || lateCount >= C_LATE_TIME;
        Boolean dLevelFlag = earlyLeaveCount >= D_LEAVE_EARLY || absentCount > D_ABSENT_TIME || lateCount >= D_LATE_TIME;

        if (dLevelFlag) {
            return ScoreLevelEnum.SCORE_D.getCode();
        } else if (cLevelFlag) {
            return ScoreLevelEnum.SCORE_C.getCode();
        } else if (sLevelFlag) {
            return ScoreLevelEnum.SCORE_S.getCode();
        } else if (aLevelFlag) {
            return ScoreLevelEnum.SCORE_A.getCode();
        } else if (bLevelFlag){
            return ScoreLevelEnum.SCORE_B.getCode();
        }
        return ScoreLevelEnum.SCORE_B.getCode();
    }


    /**
     * 获取当前用户考勤数据
     * @param workResult
     * @param nickName
     * @return
     */
    private UserKqStat getUserKqStat(TrackWorkResultVO workResult, String nickName) {
        return userKqStatMapper.selectOne(
            new LambdaQueryWrapper<UserKqStat>()
                .eq(UserKqStat::getKqYear, workResult.getWorkYear())
                .eq(UserKqStat::getKqMonth, workResult.getWorkMonth())
                .eq(UserKqStat::getKqUserName, nickName)
        );
    }

    /**
     * 获取部门的考勤数据（除组长，全月请假的成员）
     * @param workResult
     * @return
     */
    private List<UserKqStat> getAllDeptStat(TrackWorkResultVO workResult) {
        List<UserKqStat> allDeptStats = userKqStatMapper.selectList(
            new LambdaQueryWrapper<UserKqStat>()
                .eq(UserKqStat::getKqYear, workResult.getWorkYear())
                .eq(UserKqStat::getKqMonth, workResult.getWorkMonth())
                .ne(UserKqStat::getKqAttendanceDays,0)
        );
        List<String> managetNickList = sysUserService.listByRoleId(PersonTypeEnum.TECHNICAL_MANAGER.getRoleId())
            .stream().map(SysUser::getNickName).collect(Collectors.toList());

        List<UserKqStat> deptStats = allDeptStats.stream()
            .filter(userKqStat -> !managetNickList.contains(userKqStat.getKqUserName())).collect(Collectors.toList());

        return deptStats;
    }

    /**
     * 计算百分位排名
     * @param deptStats
     * @param userStat
     * @return
     */
    private double calculatePercentileRank(List<UserKqStat> deptStats, UserKqStat userStat) {
        if (deptStats == null || deptStats.isEmpty()) {
            return 0.0;
        }
        // 按日均工作时长排序
        List<Long> sortedWorkHours = deptStats.stream()
            .map(stat -> stat.getKqAttendanceWorkTime())
            .sorted(Comparator.reverseOrder())
            .collect(Collectors.toList());
        // 计算当前用户的排名
        Long userWorkHours = userStat.getKqAttendanceWorkTime();
        int rank = 1;
        for (Long hours : sortedWorkHours) {
            if (hours.compareTo(userWorkHours) <= 0) {
                break;
            }
            rank++;
        }
        return (double) rank / deptStats.size();
    }


    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        // 1. 获取当前用户考勤数据
        UserKqStat userStat = getUserKqStat(workResult, nickName);
        if (userStat == null) {
            return String.format("[%s] %s年%s月考勤数据缺失，评级：%s",
                nickName, workResult.getWorkYear(), workResult.getWorkMonth(), level);
        }

        // 2. 计算关键指标
        double dailyWorkHours = userStat.getKqAttendanceWorkTime() / 60.0 / userStat.getKqAttendanceDays();
        int lateCount = Optional.ofNullable(userStat.getKqLateCount()).orElse(0L).intValue();
        int earlyLeaveCount = Optional.ofNullable(userStat.getKqLeaveEarlyCount()).orElse(0L).intValue();
        int absentCount = Optional.ofNullable(userStat.getKqAbsenteeismDays()).orElse(0L).intValue();

        // 3. 构建日志内容
        StringBuilder logContent = new StringBuilder();
        logContent.append(String.format("[%s] %s年%s月考勤统计：",
            nickName, workResult.getWorkYear(), workResult.getWorkMonth()));

        // 添加核心指标
        logContent.append(String.format("出勤%d天，日均%.1f小时，",
            userStat.getKqAttendanceDays(), dailyWorkHours));
        logContent.append(String.format("迟到%d次，早退%d次，旷工%d天",
            lateCount, earlyLeaveCount, absentCount));

        // 添加评级信息
        logContent.append(String.format("，评级：%s", level));

        // 添加评级原因
        String reason = getRatingReason(dailyWorkHours, lateCount, earlyLeaveCount, absentCount, level);

        // 添加部门排名信息（仅S/A级显示）
        if (level.equals(ScoreLevelEnum.SCORE_S.getCode()) || level.equals(ScoreLevelEnum.SCORE_A.getCode())) {
            List<UserKqStat> deptStats = getAllDeptStat(workResult);
            double percentile = calculatePercentileRank(deptStats, userStat);
            logContent.append(String.format("，部门排名前%.0f%%", percentile * 100));
        }
        if (!reason.isEmpty()) {
            logContent.append("，原因：").append(reason);
        }
        return logContent.toString();
    }

    /**
     * 获取评级原因
     */
    private String getRatingReason(double dailyWorkHours, int lateCount,
                                   int earlyLeaveCount, int absentCount, String level) {
        switch (level) {
            case "S":
                return String.format("日均工%.1f小时且无早退，迟到%d次,达到S级标准", dailyWorkHours, lateCount);
            case "A":
                return String.format("日均工作%.1f小时且无早退，迟到%d次,达到A级标准", dailyWorkHours, lateCount);
            case "B":
                return String.format("日均工作%.1f小时且无早退，迟到%d次,达到B级标准", dailyWorkHours, lateCount);
            case "C":
                if (earlyLeaveCount >= C_LEAVE_EARLY) {
                    return String.format("早退%d,≥%d次,固为C级",earlyLeaveCount, C_LEAVE_EARLY);
                } else if (lateCount >= C_LATE_TIME) {
                    return String.format("迟到%d,≥%d次,固为C级",lateCount, C_LATE_TIME);
                } else {
                    return String.format("旷工%d,≥%d天,固为C级",absentCount, C_ABSENT_TIME);
                }
            case "D":
                if (earlyLeaveCount >= D_LEAVE_EARLY) {
                    return String.format("早退%d,≥%d次",earlyLeaveCount, D_LEAVE_EARLY);
                } else if (lateCount >= D_LATE_TIME) {
                    return String.format("迟到%d,≥%d次",lateCount, D_LATE_TIME);
                } else {
                    return String.format("旷工%d,≥%d天",absentCount, D_ABSENT_TIME);
                }
            default:
                return "";
        }
    }
}
