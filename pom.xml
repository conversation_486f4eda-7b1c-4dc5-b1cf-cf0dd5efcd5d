<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.qmqb.imp</groupId>
    <artifactId>imp-parent</artifactId>
    <version>4.4.0</version>

    <name>imp-parent</name>
    <url>https://gitee.com/JavaLionLi/RuoYi-Vue-Plus</url>
    <description>
        技术中心综合管理平台
        基于RuoYi-Vue-Plus后台管理系统4.4.0
    </description>

    <properties>
        <imp.version>4.4.0</imp.version>
        <spring-boot.version>2.7.6</spring-boot.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
        <spring-boot.mybatis>2.2.2</spring-boot.mybatis>
        <springdoc.version>1.6.13</springdoc.version>
        <knife4j.version>4.1.0</knife4j.version>
        <poi.version>5.2.3</poi.version>
        <easyexcel.version>3.1.3</easyexcel.version>
        <velocity.version>2.3</velocity.version>
        <satoken.version>1.33.0</satoken.version>
        <mybatis-plus.version>3.5.2</mybatis-plus.version>
        <p6spy.version>3.9.1</p6spy.version>
        <hutool.version>5.8.10</hutool.version>
        <okhttp.version>4.10.0</okhttp.version>
        <spring-boot-admin.version>2.7.7</spring-boot-admin.version>
        <redisson.version>3.18.0</redisson.version>
        <lock4j.version>2.2.3</lock4j.version>
        <dynamic-ds.version>3.5.2</dynamic-ds.version>
        <alibaba-ttl.version>2.14.2</alibaba-ttl.version>
        <juniversalchardet.version>1.0.3</juniversalchardet.version>
        <!--        <xxl-job.version>2.3.1</xxl-job.version>-->
        <!-- 同步现有xxl-job测试生产版本，不单独部署 -->
        <xxl-job.version>2.2.0</xxl-job.version>
        <lombok.version>1.18.24</lombok.version>

        <!-- 统一 guava 版本 解决隐式漏洞问题 -->
        <guava.version>31.1-jre</guava.version>
        <!-- 临时修复 snakeyaml 漏洞 -->
        <snakeyaml.version>1.32</snakeyaml.version>

        <!-- OSS 配置 -->
        <aws-java-sdk-s3.version>1.12.349</aws-java-sdk-s3.version>
        <!-- SMS 配置 -->
        <aliyun.sms.version>2.0.22</aliyun.sms.version>
        <tencent.sms.version>3.1.635</tencent.sms.version>
        <structure.version>2022.7-SNAPSHOT</structure.version>
        <structure.lock.version>3.0.9-SNAPSHOT</structure.lock.version>
        <structure.tool.version>3.0.9-SNAPSHOT</structure.tool.version>
        <dingtalk-oapi.version>2.0.0</dingtalk-oapi.version>
        <dingtalk-api.version>2.1.12</dingtalk-api.version>
        <TinyPinyin.version>2.0.3.RELEASE</TinyPinyin.version>
        <block-sql.starter.version>2.0.0-SNAPSHOT</block-sql.starter.version>
        <skipTests>true</skipTests>
        <silicon-flow-ai.version>1.0-SNAPSHOT</silicon-flow-ai.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- hutool 的依赖配置-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-webmvc-core</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-javadoc</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <!-- SpringDoc OpenAPI UI -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <!-- Knife4j OpenAPI3 Spring Boot Starter -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springdoc</groupId>
                        <artifactId>springdoc-openapi-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springdoc</groupId>
                        <artifactId>springdoc-openapi-webmvc-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.swagger.core.v3</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.swagger.core.v3</groupId>
                        <artifactId>swagger-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.swagger.core.v3</groupId>
                        <artifactId>swagger-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml-schemas</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- Sa-Token 权限认证, 在线文档：http://sa-token.dev33.cn/ -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>
            <!-- Sa-Token 整合 jwt -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-jwt</artifactId>
                <version>${satoken.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- dynamic-datasource 多数据源-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-ds.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!-- sql性能分析插件 -->
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>${aliyun.sms.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-sms</artifactId>
                <version>${tencent.sms.version}</version>
            </dependency>

            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!--redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
            </dependency>

            <!-- xxl-job-core -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${alibaba-ttl.version}</version>
            </dependency>

            <!-- 统一 guava 版本 解决隐式漏洞问题 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- 临时修复 snakeyaml 漏洞 -->
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>

            <!-- 架构组日志组件 -->
            <dependency>
                <groupId>com.hzed.structure</groupId>
                <artifactId>structure-log</artifactId>
                <version>${structure.version}</version>
            </dependency>
            <!-- 架构组分布式锁组件 -->
            <dependency>
                <groupId>com.hzed.structure</groupId>
                <artifactId>structure-lock</artifactId>
                <version>${structure.lock.version}</version>
            </dependency>
            <!-- 架构组分布式工具组件 -->
            <dependency>
                <groupId>com.hzed.structure</groupId>
                <artifactId>structure-tool</artifactId>
                <version>${structure.tool.version}</version>
            </dependency>

            <!-- 钉钉旧版服务端API -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>${dingtalk-oapi.version}</version>
            </dependency>

            <!-- 钉钉新版版服务端API -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dingtalk</artifactId>
                <version>${dingtalk-api.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.biezhi</groupId>
                <artifactId>TinyPinyin</artifactId>
                <version>${TinyPinyin.version}</version>
            </dependency>

            <!-- 定时任务 -->
            <dependency>
                <groupId>com.qmqb.imp</groupId>
                <artifactId>imp-job</artifactId>
                <version>${imp.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>com.qmqb.imp</groupId>
                <artifactId>imp-generator</artifactId>
                <version>${imp.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>com.qmqb.imp</groupId>
                <artifactId>imp-framework</artifactId>
                <version>${imp.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.qmqb.imp</groupId>
                <artifactId>imp-system</artifactId>
                <version>${imp.version}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>com.qmqb.imp</groupId>
                <artifactId>imp-common</artifactId>
                <version>${imp.version}</version>
            </dependency>

            <!-- OSS对象存储模块 -->
            <dependency>
                <groupId>com.qmqb.imp</groupId>
                <artifactId>imp-oss</artifactId>
                <version>${imp.version}</version>
            </dependency>

            <!-- SMS短信模块 -->
            <dependency>
                <groupId>com.qmqb.imp</groupId>
                <artifactId>imp-sms</artifactId>
                <version>${imp.version}</version>
            </dependency>

            <!-- demo模块 -->
            <dependency>
                <groupId>com.qmqb.imp</groupId>
                <artifactId>imp-demo</artifactId>
                <version>${imp.version}</version>
            </dependency>

            <!-- 编码检测 -->
            <dependency>
                <groupId>com.googlecode.juniversalchardet</groupId>
                <artifactId>juniversalchardet</artifactId>
                <version>${juniversalchardet.version}</version>
            </dependency>
            <!-- sql 审查 -->
            <dependency>
                <groupId>com.qmqb.structure</groupId>
                <artifactId>spring-boot-block-sql-starter</artifactId>
                <version>${block-sql.starter.version}</version>
            </dependency>

             <!-- 硅流AI SDK -->
            <dependency>
                <groupId>com.tool.sillicon.flow</groupId>
                <artifactId>tool-silicon-flow-ai-starter</artifactId>
                <version>${silicon-flow-ai.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>imp-admin</module>
        <module>imp-framework</module>
        <module>imp-system</module>
        <module>imp-job</module>
        <module>imp-generator</module>
        <module>imp-common</module>
        <module>imp-demo</module>
        <module>imp-extend</module>
        <module>imp-oss</module>
        <module>imp-sms</module>
        <module>imp-app</module>
    </modules>
    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.9.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>com.github.therapi</groupId>
                            <artifactId>therapi-runtime-javadoc-scribe</artifactId>
                            <version>0.15.0</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                            <version>${spring-boot.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!-- 单元测试使用 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <argLine>-Dfile.encoding=UTF-8</argLine>
                    <!-- 根据打包环境执行对应的@Tag测试方法 -->
                    <groups>${profiles.active}</groups>
                    <!-- 排除标签 -->
                    <excludedGroups>exclude</excludedGroups>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 关闭过滤 -->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 引入所有 匹配文件进行过滤 -->
                <includes>
                    <include>application*</include>
                    <include>bootstrap*</include>
                    <include>banner*</include>
                </includes>
                <!-- 启用过滤 即该资源中的变量将会被过滤器中的值替换 -->
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <!-- 私服地址 -->
    <repositories>
        <repository>
            <id>hzed-public</id>
            <name>hzed public</name>
            <url>https://nexus.qmwallet.vip/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>hzed-public</id>
            <name>hzed public</name>
            <url>https://nexus.qmwallet.vip/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>hzed-releases</id>
            <name>hzed releases</name>
            <url>https://nexus.qmwallet.vip/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>hzed-snapshots</id>
            <name>hzed snapshots</name>
            <url>https://nexus.qmwallet.vip/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>local</profiles.active>
                <logging.level>debug</logging.level>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <logging.level>debug</logging.level>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>test</profiles.active>
                <logging.level>debug</logging.level>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <logging.level>warn</logging.level>
            </properties>
        </profile>
    </profiles>

</project>


