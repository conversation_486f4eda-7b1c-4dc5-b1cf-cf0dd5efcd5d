package com.qmqb.imp.job.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.SlowMonthStats;
import com.qmqb.imp.system.domain.SlowQueryDbList;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.mapper.SlowMonthStatsMapper;
import com.qmqb.imp.system.mapper.SlowQueryDbListMapper;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 慢sql问题通知定时器
 * @date 2025/6/27 11:11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SlowSqlWarnService {

    private final SlowQueryDbListMapper slowQueryDbListMapper;
    private final SlowMonthStatsMapper slowMonthStatsMapper;
    private final DingTalkConfig dingTalkConfig;
    private final IMessageService messageService;

    @TraceId("慢sql问题通知定时任务")
    @XxlJob("slowSqlWarnJobHandler")
    public ReturnT<String> slowSqlWarnJobHandler(String param) {
        try {
            XxlJobLogger.log("慢sql问题通知定时任务...");
            log.info("慢sql问题通知定时任务");
            val sw = new StopWatch();
            sw.start();
            // 统计各库的慢sql问题数据
            List<String> warnDbList = new ArrayList<>();
            for (SlowQueryDbList slowQueryDbList : slowQueryDbListMapper.selectList()) {
                // 统计该组的未指派数据（status=0）
                Long unassignedCount = countProblemsByStatusAndDb(slowQueryDbList.getDbCode(), 0);
                // 统计该组的已指派未处理数据（status=1）
                Long assignedUnhandledCount = countProblemsByStatusAndDb(slowQueryDbList.getDbCode(), 1);
                // 只统计有慢sql的库（两个数据都为0的组不统计）
                if (unassignedCount > 0 || assignedUnhandledCount > 0) {
                    String warnInfo = String.format("%s   已指派未处理：%d条    未指派：%d条",
                        slowQueryDbList.getDbName(), assignedUnhandledCount, unassignedCount);
                    warnDbList.add(warnInfo);
                }
            }
            // 发送通知
            if (CollectionUtil.isNotEmpty(warnDbList)) {
                String groupListStr = String.join("\n", warnDbList);
                send(Constants.SLOW_SQL_WARN_TAG, groupListStr);
                XxlJobLogger.log("发送慢sql问题通知，涉及库数：{}", warnDbList.size());
                log.info("发送慢sql问题通知，涉及库数：{}", warnDbList.size());
            } else {
                log.info("所有组慢sql问题均已处理完毕，无需发送通知");
                XxlJobLogger.log("所有组慢sql问题均已处理完毕，无需发送通知");
            }
            sw.stop();
            XxlJobLogger.log("慢sql问题通知定时任务执行结束,耗时:{}ms", sw.getTotalTimeMillis());
            log.info("慢sql问题通知定时任务执行结束,耗时:{}ms", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("慢sql问题通知定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }


    /**
     * 统计数据库名和状态的慢sql数量
     *
     * @param dbName 数据库名
     * @param status 状态（0未指派，1已指派未处理，2已指派已处理）
     * @return 慢sql数量
     */
    private Long countProblemsByStatusAndDb(String dbName, Integer status) {
        try {
            return slowMonthStatsMapper.selectCount(new LambdaQueryWrapper<SlowMonthStats>()
                .eq(SlowMonthStats::getDbName, dbName)
                .eq(SlowMonthStats::getStatus, status));
        } catch (Exception e) {
            log.error("统计部门{}状态{}的问题数量异常", dbName, status, e);
            return 0L;
        }
    }

    /**
     * 发送预警
     *
     * @param template  模板
     * @param groupList 组列表信息
     */
    private void send(String template, String groupList) {
        Map<String, String> map = new HashMap<>(16);
        map.put("groupList", groupList);
        String content = StrUtil.format(template, map);
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
            .url(dingTalkConfig.getRobotUrl())
            .msgtype("text")
            .content(content)
            .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }
}
