package com.qmqb.imp.system.domain.bo.performance;

import com.qmqb.imp.common.core.domain.BaseEntity;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 绩效反馈业务对象 tb_performance_feedback
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PerformanceFeedbackBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 主反馈表ID（关联主反馈表）
     */
    private Long mainFeedbackId;

    /**
     * 所属组ID
     */
    private Long groupId;

    /**
     * 所属组
     */
    private String groupName;

    /**
     * 反馈编码
     */
    private String feedbackCode;

    /**
     * 反馈时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTime;

    /**
     * 反馈时间-起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTimeBegin;
    /**
     * 反馈时间-止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTimeEnd;

    /**
     * 一类指标
     */
    private String primaryIndicator;

    /**
     * 二类指标
     */
    private String secondaryIndicator;

    /**
     * 事件标题
     */
    private String eventTitle;

    /**
     * 事件明细
     */
    private String eventDetail;

    /**
     * 推荐绩效级别
     */
    private String recommendedLevel;

    /**
     * 推荐原因
     */
    private String recommendedReason;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 员工昵称
     */
    private String nickName;

    /**
     * 绩效年份
     */
    private Integer year;

    /**
     * 绩效月份
     */
    private Integer month;

    /**
     * 角色类型
     */
    private String personType;

    /**
     * 提交人
     */
    private String submitter;

    /**
     * 项管审核状态
     */
    private String projectManagerAuditStatus;

    /**
     * 最终审核状态
     */
    private String finalAudit;

    /**
     * 事件发生开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventStartTime;

    /**
     * 事件发生结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventEndTime;

    /**
     * 提交时间-起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTimeBegin;
    /**
     * 提交时间-止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTimeEnd;
    /**
     * 项管审核时间-起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date projectManagerAuditTimeBegin;
    /**
     * 项管审核时间-止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date projectManagerAuditTimeEnd;
    /**
     * 最终审核时间-起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finalAuditTimeBegin;
    /**
     * 最终审核时间-止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finalAuditTimeEnd;

    /**
     * 项管审核人
     */
    private String projectManagerAuditor;


    /**
     * 绩效指标结果列表
     */
    private List<Long> indicatorResultIds;

    /**
     * 是否项目经理
     */
    private Boolean isProjectManager;
}
