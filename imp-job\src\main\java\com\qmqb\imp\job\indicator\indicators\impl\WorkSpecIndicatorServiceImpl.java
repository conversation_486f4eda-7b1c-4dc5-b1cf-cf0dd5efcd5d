package com.qmqb.imp.job.indicator.indicators.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.domain.entity.SysRole;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.common.utils.spring.SpringUtils;
import com.qmqb.imp.job.indicator.GroupIndicatorCalculateManager;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.ScanProjectFile;
import com.qmqb.imp.system.domain.SlowMonthStats;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.mapper.ScanProjectFileMapper;
import com.qmqb.imp.system.mapper.SlowMonthStatsMapper;
import com.qmqb.imp.system.service.ISysUserService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025-07-08 17:13
 * 工作规范遵守情况
 */
@Service
public class WorkSpecIndicatorServiceImpl implements IndicatorLevelCalcService {

    /**
     * 开发
     * S级标准：无生产慢SQL，且代码所有级别问题已处理
     */
    private static final int S_MAX_SLOW_SQL = 0;
    private static final int S_MAX_CRITICAL_ISSUES = 0;
    /**
     * A级标准：无生产慢SQL，且代码严重级别问题已处理
     */
    private static final int A_MAX_SLOW_SQL = 0;
    private static final int A_MAX_CRITICAL_ISSUES = 0;
    /**
     * B级标准：生产慢SQL≤3条且生产慢sql全部处理，且严重代码问题未处理数<10个问题
     */
    private static final int B_MAX_SLOW_SQL = 3;
    private static final int B_MAX_CRITICAL_ISSUES = 10;
    /**
     * C级标准：生产慢SQL>5条且存在1条未处理的情况，且严重代码问题未处理数>10个问题
     */
    private static final int C_MIN_SLOW_SQL = 5;
    private static final int C_UNPROCESS_SQL = 1;
    private static final int C_MIN_CRITICAL_ISSUES = 10;
    /**
     * D级标准：生产慢SQL>5并且存在大于两条未处理的情况，条且严重代码问题未处理数>20个问题
     */
    private static final int D_MIN_SLOW_SQL = 5;
    private static final int D_UNPROCESS_SQL = 2;
    private static final int D_MIN_CRITICAL_ISSUES = 20;


    @Autowired
    SlowMonthStatsMapper slowMonthStatsMapper;

    @Autowired
    ScanProjectFileMapper scanProjectFileMapper;

    @Autowired
    ISysUserService sysUserService;

    @Autowired
    GroupIndicatorCalculateManager groupIndicatorCalculateManager;


    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.DEV_SPEC.getCode();
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        SysUser sysUser = sysUserService.selectUserByNickName(nickName);
        String roleName = sysUser.getRoles().stream().findFirst().map(SysRole::getRoleName).orElse(null);
        if (PersonTypeEnum.DEVELOPER.getDesc().equals(roleName)) {
            // 1. 获取当前月
            YearMonth currentMonth = YearMonth.of(workResult.getWorkYear(), workResult.getWorkMonth());
            //2. 获取当前月的慢SQL数量和代码质量问题
            WorkSpecMetrics metrics = calculateWorkSpecMetrics(nickName, currentMonth);
            //3. 开发评级逻辑
            return devRating(metrics.getSlowSqlCount(), metrics.getUnprocessedSlowSqlCount()
                , metrics.getCriticalAmount(), metrics.getBlockerAmount());
        } else if (PersonTypeEnum.TECHNICAL_MANAGER.getDesc().equals(roleName)) {
            //技术经理评级逻辑
            return groupIndicatorCalculateManager.caculateIndicator(workResult.getWorkYear(), workResult.getWorkMonth(),
                PerformanceIndicatorEnum.DEV_SPEC.getCode(),sysUser);

        }
        return ScoreLevelEnum.SCORE_B.getCode();
    }


    /**
     * 统计慢SQL和代码质量问题
     * @param nickName
     * @param yearMonth
     * @return
     */
    private WorkSpecMetrics calculateWorkSpecMetrics(String nickName, YearMonth yearMonth) {
        List<SlowMonthStats> slowSqlData = SpringUtils.getAopProxy(this).getSlowSqlDateByProcessor(nickName, yearMonth);
        List<ScanProjectFile> scanProjectFiles = getScanProjectFilesByHandleUser(nickName, yearMonth);
        int slowSqlCount = slowSqlData.size();
        int unprocessedSlowSqlCount = (int) slowSqlData.stream()
            .filter(stats -> CommConstants.CommonValStr.ONE.equals(stats.getStatus()))
            .count();

        long criticalAmount = scanProjectFiles.stream()
            .filter(file -> CommConstants.CommonValStr.ONE.equals(file.getStatus()))
            .mapToLong(file -> Optional.ofNullable(file.getCriticalAmount()).orElse(0L))
            .sum();

        long blockerAmount = scanProjectFiles.stream()
            .filter(file -> CommConstants.CommonValStr.ONE.equals(file.getStatus()))
            .mapToLong(file -> Optional.ofNullable(file.getBlockerAmount()).orElse(0L))
            .sum();

        return new WorkSpecMetrics(slowSqlCount, unprocessedSlowSqlCount, criticalAmount, blockerAmount);
    }

    /**
     * 慢sql和代码问题数量
     */
    @Getter
    @AllArgsConstructor
    private static class WorkSpecMetrics {
        //慢sql数量
        private final int slowSqlCount;
        //未处理的慢sql数量
        private final int unprocessedSlowSqlCount;
        //代码一般问题未处理数
        private final long criticalAmount;
        //代码严重问题未处理数
        private final long blockerAmount;
    }

    /**
     * 开发评级逻辑
     * @param slowSqlCount
     * @param unprocessedSlowSqlCount
     * @param criticalAmount
     * @param blockerAmount
     * @return
     */
    private String devRating(Integer slowSqlCount, Integer unprocessedSlowSqlCount, long criticalAmount, long blockerAmount) {
        Boolean dLevel = (slowSqlCount > D_MIN_SLOW_SQL && unprocessedSlowSqlCount > D_UNPROCESS_SQL) || blockerAmount > D_MIN_CRITICAL_ISSUES;
        Boolean cLevel = (slowSqlCount > C_MIN_SLOW_SQL && unprocessedSlowSqlCount > C_UNPROCESS_SQL) || blockerAmount > C_MIN_CRITICAL_ISSUES;
        Boolean sLevel = slowSqlCount == S_MAX_SLOW_SQL && (blockerAmount + criticalAmount) == S_MAX_CRITICAL_ISSUES;
        if (dLevel) {
            return ScoreLevelEnum.SCORE_D.getCode();
        } else if (cLevel) {
            return ScoreLevelEnum.SCORE_C.getCode();
        } else if (sLevel) {
            return ScoreLevelEnum.SCORE_S.getCode();
        } else if (slowSqlCount == A_MAX_SLOW_SQL && blockerAmount == A_MAX_CRITICAL_ISSUES) {
            return ScoreLevelEnum.SCORE_A.getCode();
        } else {
            return ScoreLevelEnum.SCORE_B.getCode();
        }
    }

    /**
     * 获取指定月份的慢SQL数据
     * @param nickName
     * @param yearMonth
     * @return
     */
    @DS(DataSource.SLOWSQL)
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true,rollbackFor = Exception.class)
    public List<SlowMonthStats> getSlowSqlDateByProcessor(String nickName,YearMonth yearMonth) {
        LocalDate startDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth();
        String startTime = startDate.toString() + " 00:00:00";
        String endTime = endDate.toString() + " 23:59:59";
        List<SlowMonthStats> slowMonthStats = slowMonthStatsMapper.selectList(new LambdaQueryWrapper<SlowMonthStats>()
            .eq(SlowMonthStats::getProcesser, nickName)
            .between(SlowMonthStats::getAssignTime, startTime, endTime));
        return slowMonthStats;
    }

    /**
     * 获取指定月份的严重代码问题数据
     * @param nickName
     * @param yearMonth
     * @return
     */
    public List<ScanProjectFile> getScanProjectFilesByHandleUser(String nickName,YearMonth yearMonth) {
        LocalDate startDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth();
        String startTime = startDate.toString() + " 00:00:00";
        String endTime = endDate.toString() + " 23:59:59";
        List<ScanProjectFile> scanProjectFiles = scanProjectFileMapper.selectList(new LambdaQueryWrapper<ScanProjectFile>()
            .eq(ScanProjectFile::getHandleUser, nickName)
            .between(ScanProjectFile::getAssignTime, startTime, endTime));
        return scanProjectFiles;
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        SysUser sysUser = sysUserService.selectUserByNickName(nickName);
        String roleName = sysUser.getRoles().stream().findFirst().map(SysRole::getRoleName).orElse(null);
        if (PersonTypeEnum.DEVELOPER.getDesc().equals(roleName)) {
            Integer year = workResult.getWorkYear();
            Integer month = workResult.getWorkMonth();
            YearMonth currentMonth = YearMonth.of(year, month);
            WorkSpecMetrics metrics = calculateWorkSpecMetrics(nickName, currentMonth);

            StringBuilder logContent = new StringBuilder();
            logContent.append(String.format("[%s]在%s年%s月份工作规范遵守情况：", nickName, year, month));
            logContent.append(String.format("慢SQL数量=%d(未处理=%d), ", metrics.getSlowSqlCount(), metrics.getUnprocessedSlowSqlCount()));
            logContent.append(String.format("代码问题(严重=%d, 一般=%d)", metrics.getBlockerAmount(), metrics.getCriticalAmount()));
            logContent.append(String.format("，评级：%s", level));
            // 获取原因
            String reason = getRatingReason(level, metrics.getSlowSqlCount(), metrics.getUnprocessedSlowSqlCount(),
                metrics.getBlockerAmount(), metrics.getCriticalAmount());
            if (reason != null) {
                logContent.append(String.format("，原因：%s", reason));
            }

            return logContent.toString();
        } else if (PersonTypeEnum.TECHNICAL_MANAGER.getDesc().equals(roleName)) {
            // 获取计算数据
            GroupIndicatorCalculateManager.CaculateDate caculateDate = groupIndicatorCalculateManager.setCaculateDate(
                workResult.getWorkYear(),
                workResult.getWorkMonth(),
                PerformanceIndicatorEnum.DEV_SPEC.getCode(),
                sysUser);
            // 生成日志内容
            StringBuilder logContent = new StringBuilder();
            logContent.append(String.format("[%s]组在%s年%s月份工作规范遵守情况：",
                nickName, workResult.getWorkYear(), workResult.getWorkMonth()));

            // 添加团队规模信息
            logContent.append(String.format("团队人数=%d，", caculateDate.getTeamMembers().size()));

            // 添加组员等级分布信息
            Map<String, Long> levelCounts = caculateDate.getLevelCountMap();
            logContent.append("组员等级分布[");
            levelCounts.forEach((k, v) -> logContent.append(String.format("%s=%d ", k, v)));
            logContent.append("]");

            // 添加评级和原因
            logContent.append(String.format("，评级：%s", level));
            String reason = groupIndicatorCalculateManager.getRatingReason(caculateDate, level);
            if (StringUtils.isNotEmpty(reason)) {
                logContent.append(String.format("，原因：%s", reason));
            }
            return logContent.toString();
        }
        return  "";

    }

    private String getRatingReason(String level, int slowSqlCount, int unprocessedSlowSqlCount,
                                   long blockerAmount, long criticalAmount) {
        switch (level) {
            case "S":
                return String.format("无生产慢SQL(%d)且所有级别代码问题已处理(严重=%d, 一般=%d)",
                    slowSqlCount, blockerAmount, criticalAmount);
            case "A":
                return String.format("无生产慢SQL(%d)且严重代码问题已处理(严重=%d)",
                    slowSqlCount, blockerAmount);
            case "B":
                return String.format("慢SQL≤%d条(实际=%d)且全部处理(未处理=%d)，严重代码问题未处理数<%d(实际=%d)",
                    B_MAX_SLOW_SQL, slowSqlCount, unprocessedSlowSqlCount,
                    B_MAX_CRITICAL_ISSUES, blockerAmount);
            case "C":
                return String.format("慢SQL>%d条(实际=%d)且存在%d条未处理(实际=%d)，严重代码问题未处理数>%d(实际=%d)",
                    C_MIN_SLOW_SQL, slowSqlCount, C_UNPROCESS_SQL, unprocessedSlowSqlCount,
                    C_MIN_CRITICAL_ISSUES, blockerAmount);
            case "D":
                return String.format("慢SQL>%d条(实际=%d)且存在超过%d条未处理(实际=%d)，严重代码问题未处理数>%d(实际=%d)",
                    D_MIN_SLOW_SQL, slowSqlCount, D_UNPROCESS_SQL, unprocessedSlowSqlCount,
                    D_MIN_CRITICAL_ISSUES, blockerAmount);
            default:
                return null;
        }
    }
}
