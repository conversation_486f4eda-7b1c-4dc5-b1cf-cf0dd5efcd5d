package com.qmqb.imp.job.indicator.indicators.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.common.utils.spring.SpringUtils;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.performance.Performance;
import com.qmqb.imp.system.domain.performance.PerformanceIndicator;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.domain.vo.ZtDocVo;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.IZtDocService;
import com.qmqb.imp.system.service.indicator.IPerformanceIndicatorService;
import com.qmqb.imp.system.service.indicator.IPerformanceService;
import com.qmqb.imp.system.service.IUserKqStatService;
import com.qmqb.imp.system.domain.UserKqStat;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 本组组员平均月新增工作相关文档指标等级计算
 * <p>
 * 针对技术经理角色，根据其管理团队的文档产出情况来评估该指标：
 * - 计算组内所有成员（排除经理本人）的有效文档数（字数≥200）
 * - 特殊规则：如果组内有任何一个人的文档指标是D级，则技术经理该指标直接评为D级
 * - 否则根据组员平均文档数确定等级
 *
 * 等级标准：
 * - S级：平均至少3篇文档
 * - A级：平均至少2篇文档
 * - B级：平均至少1篇文档
 * - C级：平均小于1篇文档
 * - D级：组内有成员文档指标为D级（一票否决）
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GroupAvgWorkDocsIndicatorServiceImpl implements IndicatorLevelCalcService {

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IZtDocService ztDocService;

    @Autowired
    private IPerformanceIndicatorService performanceIndicatorService;

    @Autowired
    private IPerformanceService performanceService;

    @Autowired
    private IUserKqStatService userKqStatService;

    /**
     * 文档字数满足200字的标准
     */
    private static final int MIN_WORD_COUNT = 200;

    /**
     * S级标准：平均至少3篇文档
     */
    private static final int S_LEVEL_AVG_DOC_COUNT = CommConstants.CommonVal.THREE;
    /**
     * A级标准：平均至少2篇文档
     */
    private static final int A_LEVEL_AVG_DOC_COUNT = CommConstants.CommonVal.TWO;
    /**
     * B级标准：平均至少1篇文档
     */
    private static final int B_LEVEL_AVG_DOC_COUNT = CommConstants.CommonVal.ONE;

    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.GROUP_AVG_WORK_DOCS.getCode();
    }

    @Override
    public IndicatorCalcResult calcLevel(String nickName, Integer month, List<TrackWorkResultVO> trackWorkResults) {
        Optional<TrackWorkResultVO> userWorkResultOpt = trackWorkResults.stream()
            .filter(r -> nickName.equals(r.getWorkUsername()))
            .findFirst();

        if (!userWorkResultOpt.isPresent()) {
            String indicatorName = PerformanceIndicatorEnum.GROUP_AVG_WORK_DOCS.getName();
            return new IndicatorCalcResult(ScoreLevelEnum.SCORE_C.getCode(),
                String.format("技术经理[%s]在%s月份的%s数据未找到", nickName, month, indicatorName));
        }

        TrackWorkResultVO userWorkResult = userWorkResultOpt.get();

        try {
            // 构建团队计算数据
            TeamCalculationData teamData = buildTeamData(userWorkResult, nickName);

            // 检查一票否决
            if (checkDlevelVeto(teamData)) {
                teamData.hasDlevelMember = true;
                String logContent = generateLogContent(teamData, ScoreLevelEnum.SCORE_D.getCode());
                return new IndicatorCalcResult(ScoreLevelEnum.SCORE_D.getCode(), logContent);
            }

            // 计算团队平均文档数
            SpringUtils.getAopProxy(this).calculateTeamAvgDocCount(teamData);

            // 计算最终等级
            String finalLevel = calculateFinalLevel(teamData);

            // 生成日志
            String logContent = generateLogContent(teamData, finalLevel);

            return new IndicatorCalcResult(finalLevel, logContent);

        } catch (Exception e) {
            log.error("计算技术经理 {} 本组组员平均月新增工作相关文档指标失败", nickName, e);
            return new IndicatorCalcResult(ScoreLevelEnum.SCORE_C.getCode(),
                String.format("技术经理[%s]本组组员平均月新增工作相关文档指标计算异常：%s", nickName, e.getMessage()));
        }
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        try {
            TeamCalculationData teamData = buildTeamData(workResult, nickName);

            if (checkDlevelVeto(teamData)) {
                return ScoreLevelEnum.SCORE_D.getCode();
            }

            SpringUtils.getAopProxy(this).calculateTeamAvgDocCount(teamData);
            return calculateFinalLevel(teamData);

        } catch (Exception e) {
            log.error("计算技术经理 {} 本组组员平均月新增工作相关文档指标失败", nickName, e);
            return ScoreLevelEnum.SCORE_C.getCode();
        }
    }

    /**
     * 构建团队计算数据
     */
    private TeamCalculationData buildTeamData(TrackWorkResultVO workResult, String nickName) {
        Integer year = workResult.getWorkYear();
        Integer month = workResult.getWorkMonth();

        TeamCalculationData data = new TeamCalculationData();
        data.managerName = nickName;
        data.year = year;
        data.month = month;

        // 获取技术经理信息
        data.manager = sysUserService.selectUserByNickName(nickName);
        if (data.manager == null) {
            throw new ServiceException(String.format("未找到技术经理信息: %s", nickName));
        }

        // 获取所有团队成员
        data.allMembers = sysUserService.selectUserByDeptId(data.manager.getDeptId());
        if (data.allMembers.isEmpty()) {
            throw new ServiceException(String.format("技术经理 %s 的团队无成员", nickName));
        }

        // 过滤请假成员
        filterAbsentMembers(data);

        // 获取团队绩效记录
        data.performanceIds = getTeamPerformanceIds(data);

        return data;
    }

    /**
     * 过滤请假成员（出勤天数为0）
     */
    private void filterAbsentMembers(TeamCalculationData data) {
        List<String> allMemberNames = data.allMembers.stream()
            .map(SysUser::getNickName)
            .collect(Collectors.toList());

        // 获取考勤数据
        List<UserKqStat> attendanceStats = userKqStatService.listByUserNamesAndKqYearAndKqMonth(
            allMemberNames, String.valueOf(data.year), String.valueOf(data.month));

        // 找出请假成员
        Set<String> absentMemberNames = attendanceStats.stream()
            .filter(stat -> stat.getKqAttendanceDays() != null && stat.getKqAttendanceDays().intValue() == CommConstants.CommonVal.ZERO)
            .map(UserKqStat::getKqUserName)
            .collect(Collectors.toSet());

        // 分离有效成员和请假成员
        data.activeMembers = data.allMembers.stream()
            .filter(member -> !absentMemberNames.contains(member.getNickName()))
            .collect(Collectors.toList());

        data.absentMembers = data.allMembers.stream()
            .filter(member -> absentMemberNames.contains(member.getNickName()))
            .collect(Collectors.toList());

        log.info("技术经理 {} 团队成员过滤：总人数{}，请假人数{}，有效人数{}",
            data.managerName, data.allMembers.size(), data.absentMembers.size(), data.activeMembers.size());
    }

    /**
     * 获取团队绩效记录ID
     */
    private List<Long> getTeamPerformanceIds(TeamCalculationData data) {
        List<Long> performanceIds = performanceService.list(new LambdaQueryWrapper<Performance>()
                .eq(Performance::getYear, data.year)
                .eq(Performance::getMonth, data.month)
                .eq(Performance::getGroupId, data.manager.getDeptId()))
                .stream()
                .map(Performance::getId)
                .collect(Collectors.toList());

        if (performanceIds.isEmpty()) {
            throw new ServiceException(String.format("技术经理 %s 的团队无绩效记录", data.managerName));
        }
        return performanceIds;
    }

    /**
     * 检查一票否决（是否有D级文档指标）
     */
    private boolean checkDlevelVeto(TeamCalculationData data) {
        List<PerformanceIndicator> docIndicators = performanceIndicatorService.list(
            new LambdaQueryWrapper<PerformanceIndicator>()
                .in(PerformanceIndicator::getPerformanceId, data.performanceIds)
                .eq(PerformanceIndicator::getIndicatorCode, PerformanceIndicatorEnum.WORK_DOCS.getCode()));

        for (PerformanceIndicator indicator : docIndicators) {
            String memberLevel = indicator.getScoreLevel();
            if (ScoreLevelEnum.SCORE_D.getCode().equals(memberLevel)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 计算团队平均文档数
     */
    @DS("zentao")
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true, rollbackFor = Exception.class)
    public void calculateTeamAvgDocCount(TeamCalculationData data) {
        if (data.activeMembers.isEmpty()) {
            log.warn("技术经理 {} 的团队无有效成员（全部请假）", data.managerName);
            data.memberDocCounts = new HashMap<>(16);
            data.totalValidDocs = CommConstants.CommonVal.ZERO;
            data.avgValidDocCount = 0.0;
            return;
        }

        YearMonth yearMonth = YearMonth.of(data.year, data.month);
        LocalDate startDate = yearMonth.atDay(CommConstants.CommonVal.ONE);
        LocalDate endDate = yearMonth.atEndOfMonth();
        String startTime = startDate.toString() + " 00:00:00";
        String endTime = endDate.toString() + " 23:59:59";

        data.memberDocCounts = new HashMap<>(CommConstants.CommonVal.SIXTEEN);
        int totalValidDocs = CommConstants.CommonVal.ZERO;

        for (SysUser member : data.activeMembers) {
            String memberName = member.getZtUserName();
            String nickName = member.getNickName();
            List<ZtDocVo> memberDocs = ztDocService.getUserDocuments(memberName, startTime, endTime);

            // 过滤出字数≥200的文档
            List<ZtDocVo> validDocs = memberDocs.stream()
                .filter(doc -> getWordCount(doc.getDraft()) >= MIN_WORD_COUNT)
                .collect(Collectors.toList());

            int validDocCount = validDocs.size();
            data.memberDocCounts.put(nickName, validDocCount);
            totalValidDocs += validDocCount;

            log.debug("成员 {} 有效文档数: {}", nickName, validDocCount);
        }

        // 计算平均数
        data.totalValidDocs = totalValidDocs;
        data.avgValidDocCount = data.activeMembers.isEmpty() ? 0.0 : (double) totalValidDocs / data.activeMembers.size();
    }

    /**
     * 统计文档纯文字字数（去除HTML标签和空格）
     */
    private int getWordCount(String htmlContent) {
        if (StringUtils.isEmpty(htmlContent)) {
            return CommConstants.CommonVal.ZERO;
        }
        try {
            // 1. 去除HTML标签
            String textOnly = Jsoup.parse(htmlContent).text();
            // 2. 去除所有空白字符后统计长度
            return textOnly.replaceAll("\\s", "").length();
        } catch (Exception e) {
            log.error("统计文档字数失败", e);
            return CommConstants.CommonVal.ZERO;
        }
    }

    /**
     * 计算最终等级
     */
    private String calculateFinalLevel(TeamCalculationData data) {
        if (data.avgValidDocCount >= S_LEVEL_AVG_DOC_COUNT) {
            return ScoreLevelEnum.SCORE_S.getCode();
        } else if (data.avgValidDocCount >= A_LEVEL_AVG_DOC_COUNT) {
            return ScoreLevelEnum.SCORE_A.getCode();
        } else if (data.avgValidDocCount >= B_LEVEL_AVG_DOC_COUNT) {
            return ScoreLevelEnum.SCORE_B.getCode();
        } else {
            return ScoreLevelEnum.SCORE_C.getCode();
        }
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        try {
            TeamCalculationData data = buildTeamData(workResult, nickName);

            if (checkDlevelVeto(data)) {
                data.hasDlevelMember = true;
            } else {
                SpringUtils.getAopProxy(this).calculateTeamAvgDocCount(data);
            }

            return generateLogContent(data, level);
        } catch (Exception e) {
            log.error("生成技术经理 {} 本组组员平均月新增工作相关文档指标日志失败", nickName, e);
            return String.format("技术经理[%s]在%s年%s月份的本组组员平均月新增工作相关文档(≥200字)指标：日志生成异常，评级：%s",
                nickName, workResult.getWorkYear(), workResult.getWorkMonth(), level);
        }
    }

    /**
     * 生成详细日志内容
     */
    private String generateLogContent(TeamCalculationData data, String level) {
        StringBuilder logContent = new StringBuilder();

        logContent.append(String.format("[%s]在%s年%s月份的本组组员平均月新增工作相关文档(≥200字)指标",
            data.managerName, data.year, data.month));

        // 基本统计信息
        logContent.append(String.format("，团队总人数：%d人", data.allMembers.size()));
        logContent.append(String.format("，有效成员数(排除请假)：%d人", data.activeMembers.size()));
        logContent.append(String.format("，团队总文档数(≥200字)：%d篇", data.totalValidDocs));
        logContent.append(String.format("，平均文档数(≥200字)：%.1f篇", data.avgValidDocCount));
        logContent.append("，评级：").append(level);

        // 成员文档分布
        appendMemberDocDistribution(logContent, data);

        // 请假成员信息
        if (!data.absentMembers.isEmpty()) {
            logContent.append("\n  请假成员（已排除）：");
            for (SysUser absentMember : data.absentMembers) {
                logContent.append(String.format("\n    %s：当月请假未上班", absentMember.getNickName()));
            }
        }

        // 评级原因
        String reason = getRatingReason(level, data);
        if (reason != null) {
            logContent.append("\n  计算原因：").append(reason);
        }

        return logContent.toString();
    }

    /**
     * 添加成员文档分布统计
     */
    private void appendMemberDocDistribution(StringBuilder logContent, TeamCalculationData data) {
        if (data.memberDocCounts != null && !data.memberDocCounts.isEmpty()) {
            logContent.append("\n  成员文档分布：");

            // 按文档数排序显示
            List<Map.Entry<String, Integer>> sortedEntries = data.memberDocCounts.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .collect(Collectors.toList());

            for (Map.Entry<String, Integer> entry : sortedEntries) {
                logContent.append(String.format("\n    %s：%d篇", entry.getKey(), entry.getValue()));
            }
        }
    }

    /**
     * 获取评级原因
     */
    private String getRatingReason(String level, TeamCalculationData data) {
        if (data.hasDlevelMember) {
            return "组内有成员文档指标为D级";
        }

        int teamSize = data.activeMembers.size();
        double avgCount = data.avgValidDocCount;

        switch (level) {
            case "S":
                return String.format("团队%d人平均%.1f篇有效文档，达到S级标准(≥%d篇)", teamSize, avgCount, S_LEVEL_AVG_DOC_COUNT);
            case "A":
                return String.format("团队%d人平均%.1f篇有效文档，达到A级标准(≥%d篇)", teamSize, avgCount, A_LEVEL_AVG_DOC_COUNT);
            case "B":
                return String.format("团队%d人平均%.1f篇有效文档，达到B级标准(≥%d篇)", teamSize, avgCount, B_LEVEL_AVG_DOC_COUNT);
            case "C":
                return String.format("团队%d人平均%.1f篇有效文档，当月无", teamSize, avgCount);
            case "D":
                return "组内有成员文档指标为D级";
            default:
                return null;
        }
    }

    /**
     * 团队计算数据类
     */
    private static class TeamCalculationData {
        String managerName;
        Integer year;
        Integer month;
        SysUser manager;
        List<SysUser> allMembers;
        List<SysUser> activeMembers;
        List<SysUser> absentMembers;
        List<Long> performanceIds;

        boolean hasDlevelMember;
        Map<String, Integer> memberDocCounts;
        int totalValidDocs;
        double avgValidDocCount;
    }
}
