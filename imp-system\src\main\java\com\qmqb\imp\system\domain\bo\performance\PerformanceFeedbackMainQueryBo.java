package com.qmqb.imp.system.domain.bo.performance;

import com.qmqb.imp.common.core.domain.BaseEntity;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 绩效反馈主表业务对象 tb_performance_feedback_main
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PerformanceFeedbackMainQueryBo extends BaseEntity {
    /**
     * 反馈编码
     */
    private String feedbackCode;

    /**
     * 一类指标
     */
    @NotBlank(message = "一类指标不能为空", groups = {AddGroup.class, EditGroup.class})
    private String primaryIndicator;

    /**
     * 二类指标
     */
    @NotBlank(message = "二类指标不能为空", groups = {AddGroup.class, EditGroup.class})
    private String secondaryIndicator;

    /**
     * 项管审核状态
     */
    private String projectManagerAuditStatus;

    /**
     * 最终审核状态
     */
    private String finalAudit;

    /**
     * 员工昵称
     */
    private String nickName;

    /**
     * 组ID
     */
    private Long groupId;

    /**
     * 推荐绩效级别
     */
    private String recommendedLevel;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 提交状态
     */
    private String submitStatus;

    /**
     * 反馈时间-起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTimeBegin;
    /**
     * 反馈时间-止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTimeEnd;

    /**
     * 事件发生时间-开始
     */
    @NotNull(message = "事件发生时间-开始不能为空", groups = {AddGroup.class, EditGroup.class})
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventStartTime;
    /**
     * 事件发生时间-结束
     */
    @NotNull(message = "事件发生时间-结束不能为空", groups = {AddGroup.class, EditGroup.class})
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventEndTime;
    /**
     * 提交时间-起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTimeBegin;
    /**
     * 提交时间-止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTimeEnd;

    /**
     * 项管审核时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date projectManagerAuditTime;

    /**
     * 项管审核时间-起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date projectManagerAuditTimeBegin;
    /**
     * 项管审核时间-止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date projectManagerAuditTimeEnd;

    /**
     * 最终审核时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finalAuditTime;

    /**
     * 最终审核时间-起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finalAuditTimeBegin;
    /**
     * 最终审核时间-止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finalAuditTimeEnd;

    /**
     * 是否管理员
     */
    private Boolean isAdmin;
    /**
     * 是否技术总监
     */
    private Boolean isJszxAdmin;
    /**
     * 是否项目经理
     */
    private Boolean isProjectManager;
    /**
     * 当前用户名
     */
    private String userName;
}
