package com.qmqb.imp.job.indicator;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.PerformanceFeedbackAuditStatusEnum;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.bo.TrackWorkResultBO;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain;
import com.qmqb.imp.system.domain.performance.PerformanceTutoring;
import com.qmqb.imp.system.domain.vo.DepartmentWarnAnalysisVo;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.service.ISysDeptService;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.ITrackWorkResultService;
import com.qmqb.imp.system.service.IUserKqStatService;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackMainService;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackService;
import com.qmqb.imp.system.service.indicator.IPerformanceTutoringService;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 绩效反馈生成任务管理器
 * 独立生成绩效反馈记录，不依赖绩效主表
 *
 * <AUTHOR>
 */
@Service
public class PerformanceFeedbackManager {

    @Autowired
    private IndicatorLevelCalcRegistry registry;

    @Autowired
    private IPerformanceFeedbackService performanceFeedbackService;

    @Autowired
    private ITrackWorkResultService trackWorkResultService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private PerformanceFeedbackGenerator performanceFeedbackGenerator;

    @Autowired
    private PerformanceFeedbackMainGenerator performanceFeedbackMainGenerator;

    @Autowired
    private IPerformanceFeedbackMainService performanceFeedbackMainService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IPerformanceTutoringService perfTutoringService;

    /**
     * 为指定年月和部门的员工生成绩效反馈记录
     *
     * @param year   年份
     * @param month  月份
     * @param pType  表示当前逻辑是跑哪个人员类型 0非组长，1组长
     * @param deptId 部门ID，如果为null则处理所有部门
     */
    public void generateFeedbackByYearMonthAndDept(Integer year, Integer month, Integer pType, Long deptId) {
        // 1. 获取部门ID列表
        List<Long> groupIdList;
        if (deptId != null) {
            // 指定部门ID
            groupIdList = new ArrayList<>();
            groupIdList.add(deptId);
        } else {
            // 获取所有技术中心的部门ID
            groupIdList = sysDeptService.listTecCenterDeptIdList();
        }

        // 2. 获取工作成果数据
        TrackWorkResultBO request = new TrackWorkResultBO();
        request.setPageNum(1);
        request.setPageSize(1000);
        request.setEvalYear(year);
        request.setEvalMonth(month);
        if (deptId != null) {
            request.setGroupId(deptId);
        }

        Page<TrackWorkResultVO> page = new Page<>(request.getPageNum(), request.getPageSize());
        TableDataInfo<TrackWorkResultVO> trackWorkResultVoTableDataInfo =
            trackWorkResultService.getTrackWorkResultVoTableDataInfo(request, page, groupIdList, year, month);

        // 3. 按角色分类处理数据
        Map<Long, PersonTypeEnum> roleIdMap = getRoleIdMap();
        Map<PersonTypeEnum, List<TrackWorkResultVO>> roleDataMap = groupDataByRole(trackWorkResultVoTableDataInfo.getRows(), roleIdMap);

        // 4. 为每个角色类型的员工生成绩效反馈
        for (Map.Entry<PersonTypeEnum, List<TrackWorkResultVO>> entry : roleDataMap.entrySet()) {
            PersonTypeEnum personType = entry.getKey();
            //组长的数据过滤掉，因为组长的反馈数据是需要组员的数据来计算，所以统一放到绩效指标统计定时任务calcPerformanceIndicatorJobHandler处理
            if (pType == 1 && !PersonTypeEnum.TECHNICAL_MANAGER.getType().equals(personType.getType())) {
                continue;
            }
            //如果跑非组长的数据，则跳过组长的数据
            if (pType == 0 && PersonTypeEnum.TECHNICAL_MANAGER.getType().equals(personType.getType())) {
                continue;
            }
            List<TrackWorkResultVO> trackWorkResults = entry.getValue();

            for (TrackWorkResultVO workResult : trackWorkResults) {
                ((PerformanceFeedbackManager) AopContext.currentProxy())
                    .generateFeedbackForEmployee(workResult.getWorkUsername(), year, month, personType, trackWorkResults);
            }
        }
    }

    /**
     * 为员工生成绩效反馈记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateFeedbackForEmployee(String nickName, Integer year, Integer month,
                                            PersonTypeEnum personTypeEnum,
                                            List<TrackWorkResultVO> trackWorkResults) {
        // 1. 删除该员工该月份的旧反馈记录,绩效辅导记录
        List<String> idByNickNameAndYearMonth = performanceFeedbackMainService.getIdByNickNameAndYearMonth(nickName, year, month);
        perfTutoringService.removeByMainIds(idByNickNameAndYearMonth);
        performanceFeedbackService.removeByMainIds(idByNickNameAndYearMonth);
        performanceFeedbackMainService.removeBatchByIds(idByNickNameAndYearMonth);

        // 3. 获取用户信息（用于获取组信息）
        SysUser sysUser = sysUserService.selectUserByNickName(nickName);
        Long groupId = null;
        String groupName = null;
        if (sysUser != null && sysUser.getDept() != null) {
            groupId = sysUser.getDept().getDeptId();
            groupName = sysUser.getDept().getDeptName();
        }

        // 4. 计算所有指标等级
        List<IndicatorLevelCalcService> serviceList = registry.getAllByRole(personTypeEnum);
        List<PerformanceFeedbackMain> feedbackMainList = new ArrayList<>();
        List<PerformanceFeedback> feedbackList = new ArrayList<>();
        List<PerformanceTutoring> tutoringList = new ArrayList<>();

        for (IndicatorLevelCalcService service : serviceList) {
            String code = service.getIndicatorCode();
            IndicatorLevelCalcService.IndicatorCalcResult calcResult = service.calcLevel(nickName, month, trackWorkResults);

            //创建主反馈记录
            PerformanceFeedbackMain mainFeedback = performanceFeedbackMainGenerator.createMainFeedbackRecord(
                nickName, year, month, String.valueOf(personTypeEnum.getType()), code, calcResult, sysUser);

            // 5. 生成绩效反馈记录（基于主表编码，包含组信息）
            PerformanceFeedback feedback = performanceFeedbackGenerator.createFeedbackRecord(
                nickName, year, month, personTypeEnum, code, calcResult, groupId, groupName);


            if (ScoreLevelEnum.SCORE_B.getCode().equals(feedback.getRecommendedLevel())) {
                // 合格绩效不生成绩效反馈
                continue;
            }
            performanceFeedbackMainService.save(mainFeedback);
            feedbackMainList.add(mainFeedback);
            // 设置主表关联ID
            feedback.setMainFeedbackId(mainFeedback.getId());
            feedbackList.add(feedback);
        }

        // 6. 保存反馈记录
        if (!feedbackList.isEmpty()) {
            performanceFeedbackService.saveBatch(feedbackList);
        }

        //7. 针对项管无需审核且为cd绩效的数据生成辅导记录
        Map<Long, PerformanceFeedbackMain> performanceFeedbackMainMap =  feedbackMainList.stream().collect(Collectors.toMap(PerformanceFeedbackMain::getId, a -> a));
        for(PerformanceFeedback feedback : feedbackList){
            PerformanceFeedbackMain main = performanceFeedbackMainMap.getOrDefault(feedback.getMainFeedbackId(), null);
            if (main == null) {
                throw new ServiceException("主反馈记录不存在");
            }
            if (main.getProjectManagerAuditStatus().equals(PerformanceFeedbackAuditStatusEnum.APPROVED.getCode())) {
                if (feedback.getRecommendedLevel().equals(ScoreLevelEnum.SCORE_C.getCode()) ||
                    feedback.getRecommendedLevel().equals(ScoreLevelEnum.SCORE_D.getCode())) {
                    PerformanceTutoring tutoring = PerformanceTutoring.builder().feedbackId(feedback.getId()).build();
                    tutoringList.add(tutoring);
                }
            }
        }
        if (!tutoringList.isEmpty()) {
            perfTutoringService.saveBatch(tutoringList);
        }
    }

    /**
     * 获取角色ID映射
     */
    private Map<Long, PersonTypeEnum> getRoleIdMap() {
        return java.util.Arrays.stream(PersonTypeEnum.values())
            .filter(e -> e.getRoleId() != null)
            .collect(java.util.stream.Collectors.toMap(PersonTypeEnum::getRoleId, Function.identity()));
    }

    /**
     * 按角色分组数据
     */
    private Map<PersonTypeEnum, List<TrackWorkResultVO>> groupDataByRole(List<TrackWorkResultVO> rows, Map<Long, PersonTypeEnum> roleIdMap) {
        return rows.stream()
            .map(vo -> {
                if (vo.getRoleIds() == null) {
                    return null;
                }
                try {
                    // 支持多个角色ID，按逗号分割
                    List<Long> roleIdList = java.util.Arrays.stream(vo.getRoleIds().split(","))
                        .filter(roleIdStr -> !roleIdStr.trim().isEmpty())
                        .map(Long::parseLong)
                        .collect(java.util.stream.Collectors.toList());
                    // 遍历所有角色ID，找到第一个匹配的PersonTypeEnum
                    for (Long roleId : roleIdList) {
                        PersonTypeEnum personType = roleIdMap.get(roleId);
                        if (personType != null) {
                            // 返回第一个匹配的角色类型
                            return new java.util.AbstractMap.SimpleImmutableEntry<>(personType, vo);
                        }
                    }
                    // 没有匹配的角色类型，返回null
                    return null;
                } catch (NumberFormatException e) {
                    // roleIds 不是有效的数字格式，返回 null
                    return null;
                }
            })
            .filter(entry -> entry != null && entry.getKey() != null)
            .collect(java.util.stream.Collectors.groupingBy(java.util.Map.Entry::getKey,
                java.util.stream.Collectors.mapping(java.util.Map.Entry::getValue, java.util.stream.Collectors.toList())));
    }
}
