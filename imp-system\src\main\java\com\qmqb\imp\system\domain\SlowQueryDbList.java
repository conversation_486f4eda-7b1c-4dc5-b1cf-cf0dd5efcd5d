package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 分支详细信息对象 tb_branch
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@Data
@TableName("t_slow_query_db_list")
public class SlowQueryDbList implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;
    /**
     * dbCode
     */
    @TableField("db_code")
    private String dbCode;
    /**
     * dbName
     */
    @TableField("db_name")
    private String dbName;
    /**
     * 慢SQL表名称
     */
    @TableField("tab_name")
    private String tabName;
    /**
     * TOP SQL表名称
     */
    @TableField("top_name")
    private String topName;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

}
