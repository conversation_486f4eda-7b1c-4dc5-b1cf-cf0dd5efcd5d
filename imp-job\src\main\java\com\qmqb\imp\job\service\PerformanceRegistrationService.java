package com.qmqb.imp.job.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.system.domain.PerformanceRegistration;
import com.qmqb.imp.system.domain.performance.Performance;
import com.qmqb.imp.system.mapper.PerformanceRegistrationMapper;
import com.qmqb.imp.system.mapper.performance.PerformanceMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 绩效登记任务
 * @date 2025/7/7 8:41
 */
@Component
@Slf4j
public class PerformanceRegistrationService {

    @Resource
    private PerformanceRegistrationMapper performanceRegistrationMapper;

    @Resource
    private PerformanceMapper performanceMapper;


    /**
     * 绩效登记任务
     */
    @TraceId("绩效登记任务")
    @XxlJob("performanceRegistrationJobHandler")
    public ReturnT<String> performanceRegistrationJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行绩效登记定时任务...");
            log.info("开始执行绩效登记定时任务...");
            StopWatch sw = new StopWatch();
            sw.start();
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, -1);
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH);
            List<Performance> performanceList = performanceMapper.selectList(new LambdaQueryWrapper<Performance>()
                .eq(Performance::getYear, year).eq(Performance::getMonth, month));
            if (CollectionUtils.isNotEmpty(performanceList)) {
                List<PerformanceRegistration> saveDates = performanceList.stream().filter(performance -> StringUtils.equalsAny(performance.getFinalLevel(),
                    ScoreLevelEnum.SCORE_S.getCode(), ScoreLevelEnum.SCORE_A.getCode(),ScoreLevelEnum.SCORE_C.getCode(),ScoreLevelEnum.SCORE_D.getCode())).map(performance -> {
                    PerformanceRegistration performanceRegistration = new PerformanceRegistration();
                    performanceRegistration.setDeptId(performance.getGroupId());
                    performanceRegistration.setDeptName(performance.getGroupName());
                    performanceRegistration.setUserName(performance.getNickName());
                    performanceRegistration.setEvalYear(String.valueOf(performance.getYear()));
                    performanceRegistration.setEvalMonth(String.format("%02d", performance.getMonth()));
                    performanceRegistration.setLevel(performance.getFinalLevel());
                    performanceRegistration.setDelFlag("0");
                    return performanceRegistration;
                }).collect(Collectors.toList());
                performanceRegistrationMapper.insertOrUpdateBatch(saveDates);
            }
            sw.stop();
            XxlJobLogger.log("绩效登记定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("绩效登记定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("绩效登记定时任务执行异常: {}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
    }
}
