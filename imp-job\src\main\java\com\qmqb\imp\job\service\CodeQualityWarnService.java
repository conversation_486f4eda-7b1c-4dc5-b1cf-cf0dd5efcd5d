package com.qmqb.imp.job.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.mapper.ScanProjectFileMapper;
import com.qmqb.imp.system.service.ISysDeptService;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 代码质量问题通知定时器
 *
 * <AUTHOR>
 * @since 2024/12/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CodeQualityWarnService {

    private final ScanProjectFileMapper scanProjectFileMapper;
    private final ISysDeptService sysDeptService;
    private final DingTalkConfig dingTalkConfig;
    private final IMessageService messageService;

    @TraceId("代码质量问题通知定时任务")
    @XxlJob("codeQualityWarnServiceJobHandler")
    public ReturnT<String> codeQualityWarnServiceJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行代码质量问题通知定时任务...");
            log.info("开始执行代码质量问题通知定时任务");
            val sw = new StopWatch();
            sw.start();

            // 获取技术中心下的所有子部门
            List<Long> groupIdList = sysDeptService.listTecCenterDeptIdList();
            // 过滤新员工组
            groupIdList = groupIdList.stream()
                    .filter(groupId -> !UserConstants.NEW_MEM_DEPT_ID.equals(groupId))
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(groupIdList)) {
                log.info("未找到技术中心部门信息");
                return ReturnT.SUCCESS;
            }

            // 构建部门ID到部门名称的映射
            Map<Long, String> deptIdToNameMap = new HashMap<>(16);
            for (Long deptId : groupIdList) {
                SysDept dept = sysDeptService.selectDeptById(deptId);
                if (dept != null) {
                    deptIdToNameMap.put(deptId, dept.getDeptName());
                }
            }

            // 统计各组的代码问题数据
            List<String> warnGroupList = new ArrayList<>();

            for (Long deptId : groupIdList) {
                String deptName = deptIdToNameMap.get(deptId);
                if (StrUtil.isBlank(deptName)) {
                    continue;
                }

                // 统计该组的未指派数据（status=0）
                Long unassignedCount = countProblemsByStatusAndDept(deptId, 0);

                // 统计该组的已指派未处理数据（status=1）
                Long assignedUnhandledCount = countProblemsByStatusAndDept(deptId, 1);

                // 只统计有问题的组（两个数据都为0的组不统计）
                if (unassignedCount > 0 || assignedUnhandledCount > 0) {
                    String warnInfo = String.format("%s   已指派未处理：%d条    未指派：%d条",
                            deptName, assignedUnhandledCount, unassignedCount);
                    warnGroupList.add(warnInfo);
                }
            }

            // 发送通知
            if (CollectionUtil.isNotEmpty(warnGroupList)) {
                String groupListStr = String.join("\n", warnGroupList);
                send(Constants.CODE_QUALITY_WARN_TAG, groupListStr);
                XxlJobLogger.log("发送代码质量问题通知，涉及组数：{}", warnGroupList.size());
                log.info("发送代码质量问题通知，涉及组数：{}", warnGroupList.size());
            } else {
                log.info("所有组代码质量问题均已处理完毕，无需发送通知");
                XxlJobLogger.log("所有组代码质量问题均已处理完毕，无需发送通知");
            }

            sw.stop();
            XxlJobLogger.log("代码质量问题通知定时任务执行结束,耗时:{}ms", sw.getTotalTimeMillis());
            log.info("代码质量问题通知定时任务执行结束,耗时:{}ms", sw.getTotalTimeMillis());

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("代码质量问题通知定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 统计指定部门和状态的问题数量
     *
     * @param deptId 部门ID
     * @param status 状态（0未指派，1已指派未处理，2已指派已处理）
     * @return 问题数量
     */
    private Long countProblemsByStatusAndDept(Long deptId, Integer status) {
        try {
            return scanProjectFileMapper.countByStatusAndDept(String.valueOf(deptId), String.valueOf(status));
        } catch (Exception e) {
            log.error("统计部门{}状态{}的问题数量异常", deptId, status, e);
            return 0L;
        }
    }

    /**
     * 发送预警
     *
     * @param template  模板
     * @param groupList 组列表信息
     */
    private void send(String template, String groupList) {
        Map<String, String> map = new HashMap<>(16);
        map.put("groupList", groupList);
        String content = StrUtil.format(template, map);
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
                .url(dingTalkConfig.getRobotUrl())
                .msgtype("text")
                .content(content)
                .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }
}
