package com.qmqb.imp.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.domain.dto.TaskQueryDTO;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.ZtTask;
import com.qmqb.imp.system.domain.bo.WorkDetailBo;
import com.qmqb.imp.system.domain.vo.TaskInfoVO;
import com.qmqb.imp.system.domain.vo.TaskVO;
import com.qmqb.imp.system.domain.vo.ZtTaskCountVO;
import com.qmqb.imp.system.domain.vo.ZtTaskVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 禅道任务Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@DS(DataSource.ZENTAO)
public interface ZtTaskMapper extends BaseMapperPlus<ZtTaskMapper, ZtTask, ZtTaskVo> {

    /**
     * 查询数据
     * @param request
     * @return
     */
    List<TaskVO> getLists(@Param("request") TaskQueryDTO request);

    /**
     * 通过分页插件的方式获取任务数据列表
     * @param page
     * @param request
     * @return
     */
    List<TaskVO> getListsByPage(@Param("page") Page<TaskVO> page, @Param("request") TaskQueryDTO request);

    /**
     * 根据用户拼音名小写列表以及开始和结束时间获取用户的任务个数列表
     *
     * @param userNameList
     * @param beginTime
     * @param endTime
     * @return
     */
    List<ZtTaskCountVO> getTaskListByTimeAndUser(@Param("userNameList") List<String> userNameList, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 根据月份和组别查询任务列表
     *
     * @param userNameList
     * @param beginTime
     * @param endTime
     * @param status
     * @param name         任务名称 模糊查询
     * @param page
     * @return
     */
    List<TaskInfoVO> getTaskListByGroupAndMonth(@Param("userNameList") List<String> userNameList, @Param("beginTime") Date beginTime,
                                                @Param("endTime") Date endTime, @Param("status") String status, @Param("name") String name, @Param("page") Page page);

    /**
     * 根据多个id查询
     *
     * @param ids
     * @return
     */
    List<ZtTaskVo> listByIds(@Param("ids") Set<Integer> ids);

    /**
     * 根据启动时间查询进行中的任务
     *
     * @param usernames
     * @param beginTime
     * @param endTime
     * @return
     */
    List<ZtTask> queryDoingTaskByStartedTime(@Param("usernames") List<String> usernames, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);


    /**
     * 根据 年份和月份查询技术任务数
     * @param userNameList
     * @param yearList
     * @return
     */
    List<Map<String,Object>> getTechnologyTaskCounts(@Param("userNameList") List<String> userNameList, @Param("yearList") List<String> yearList);

    /**
     * 根据状态和时间段查询任务数量
     * @param status
     * @param beginTime
     * @param endTime
     * @param userNameList
     * @return
     */
    Long selectTaskCountByStatus(@Param("status") String status,@Param("beginTime") Date beginTime, @Param("endTime") Date endTime,@Param("userNameList") List<String> userNameList);


    /**
     * 个人任务明细
     * @param page
     * @param workDetail
     * @return
     */
    Page<TaskVO> queryUserTask(@Param("page") Page<TaskVO> page, @Param("workDetail") WorkDetailBo workDetail);


    /**
     * 查询进行中的任务超过10天没有完成
     * @param symbol
     * @param compareValue
     * @return
     */
    List<TaskVO> doingTaskTimeout(@Param("symbol") String symbol, @Param("compareValue") BigDecimal compareValue);

    /**
     * 查询超时暂停任务
     *
     * @param date
     * @param usernameList
     * @return
     */
    Long selectPauseTimeoutCount(@Param("date") Date date,@Param("usernameList")List<String> usernameList);

}
