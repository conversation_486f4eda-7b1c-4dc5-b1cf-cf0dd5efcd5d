package com.qmqb.imp.web.controller.system;

import com.qmqb.imp.common.config.FileStorageProperties;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.exception.ServiceException;
import com.qmqb.imp.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.StringJoiner;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @since 2025-07-02 14:14
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/files")
public class SysFileController {

    private final FileStorageProperties fileStorageProperties;

    /**
     * 单文件上传
     * @param file
     * @return
     */
    @PostMapping("/upload")
    public R<String> uploadAttachment(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return R.fail("上传文件不能为空");
        }
        // 检查文件大小
        long maxFileSize = 200 * 1024 * 1024;
        if (file.getSize() > maxFileSize) {
            return R.fail("文件大小不能超过200MB");
        }
        try {
            String originalFilename = file.getOriginalFilename();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
            String datePath = dateFormat.format(new Date());
            String fileName = System.currentTimeMillis() + "_" + originalFilename;
            // 使用配置的路径
            String fullDirPath = fileStorageProperties.getBaseDir() + fileStorageProperties.getPerformanceTutoring() + datePath;
            File dir = new File(fullDirPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            File dest = new File(fullDirPath + "/" + fileName);
            file.transferTo(dest);
            // 使用配置的访问路径
            return R.ok("文件上传成功！",
                 fileStorageProperties.getPerformanceTutoring() + datePath + "/" + fileName);
        } catch (IOException e) {
            return R.fail("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 多文件上传
     * @param files MultipartFile数组
     * @return 统一返回结果，包含逗号分隔的文件路径字符串
     */
    @PostMapping("/uploadBatch")
    public R<String> uploadAttachments(@RequestParam("files") MultipartFile[] files) {
        if (files == null || files.length == 0) {
            return R.fail("上传文件不能为空");
        }
        // 检查每个文件
        long maxFileSize = 200 * 1024 * 1024;
        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                return R.fail("包含空文件");
            }
            if (file.getSize() > maxFileSize) {
                return R.fail("文件大小不能超过200MB");
            }
        }
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
            String datePath = dateFormat.format(new Date());
            // 使用配置的路径
            String baseDir = fileStorageProperties.getBaseDir();
            String subDir = fileStorageProperties.getPerformanceTutoring();
            String fullDirPath = baseDir + subDir + datePath;
            File dir = new File(fullDirPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            StringJoiner filePaths = new StringJoiner(",");

            for (MultipartFile file : files) {
                String originalFilename = file.getOriginalFilename();
                String fileName = System.currentTimeMillis() + "_" + originalFilename;
                File dest = new File(fullDirPath + File.separator + fileName);
                file.transferTo(dest);
                filePaths.add(subDir + datePath + "/" + fileName);
            }
            return R.ok("文件上传成功！", filePaths.toString());
        } catch (IOException e) {
            return R.fail("文件上传失败: " + e.getMessage());
        }
    }


    /**
     * 下载多个文件（压缩包形式）
     * @param filePaths 逗号分隔的文件路径字符串
     * @param response
     */
    @GetMapping("/downloadBatch")
    public void downloadBatch(@RequestParam String filePaths, HttpServletResponse response) throws IOException {
        // 参数校验
        if (StringUtils.isEmpty(filePaths)) {
            throw new ServiceException("文件路径不能为空");
        }

        // 生成带精确时间戳的压缩包名称 (格式: 文件包_年月日时分秒毫秒.zip)
        String timestamp = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date());
        String zipFileName = String.format("attachment_%s.zip", timestamp);

        // 设置响应头（处理中文文件名编码）
        response.setContentType("application/zip");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition",
            "attachment; filename=" + URLEncoder.encode(zipFileName, "UTF-8") +
                ";filename*=UTF-8''" + URLEncoder.encode(zipFileName, "UTF-8"));
        // 获取基础存储路径
        String baseDir = fileStorageProperties.getBaseDir();
        try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
            // 使用LinkedHashMap保持文件顺序并记录重复
            Map<String, Integer> fileCountMap = new LinkedHashMap<>();
            String[] splitFiles = filePaths.split(",");
            for (String filePath : splitFiles) {
                // 安全路径处理
                String sanitizedPath = baseDir + filePath.trim()
                    .replace("../", "")
                    .replace("..\\", "");
                //跳过非法路径
                if (!sanitizedPath.startsWith(baseDir)) {
                    continue;
                }
                File sourceFile = new File(sanitizedPath);
                if (!sourceFile.exists() || !sourceFile.isFile()) {
                    continue;
                }
                // 处理重复文件名（添加计数后缀）
                String originalName = sourceFile.getName();
                int count = fileCountMap.getOrDefault(originalName, 0);
                fileCountMap.put(originalName, count + 1);
                String entryName = count == 0 ? originalName :
                    String.format("%d_%s", count, originalName);
                // 添加文件到压缩包
                try (BufferedInputStream bis = new BufferedInputStream(
                    new FileInputStream(sourceFile))) {

                    ZipEntry zipEntry = new ZipEntry(entryName);
                    zipEntry.setTime(sourceFile.lastModified());
                    zipOut.putNextEntry(zipEntry);

                    byte[] buffer = new byte[8192];
                    int length;
                    while ((length = bis.read(buffer)) > 0) {
                        zipOut.write(buffer, 0, length);
                    }
                    zipOut.closeEntry();
                }
            }
            zipOut.finish();
        } catch (Exception e) {
            throw new ServiceException("文件打包失败: " + e.getMessage());
        }
    }

    /**
     * 文件删除
     * @param filePath
     * @return
     */
    @DeleteMapping("/delete")
    public R<String> deleteFile(@RequestParam("filePath") String filePath) {
        try {
            if (filePath == null || filePath.isEmpty()) {
                return R.fail("文件路径不能为空");
            }
            // 使用配置的路径处理
            String prefix = fileStorageProperties.getPerformanceTutoring();
            String relativePath = filePath.replaceFirst("^" + prefix, "");

            Path fullPath = Paths.get(
                fileStorageProperties.getBaseDir() + fileStorageProperties.getPerformanceTutoring(),
                relativePath).normalize();

            File file = fullPath.toFile();
            File baseDir = new File(fileStorageProperties.getBaseDir() + fileStorageProperties.getPerformanceTutoring()).getCanonicalFile();

            if (!file.getCanonicalFile().toPath().startsWith(baseDir.toPath())) {
                return R.fail("非法文件路径");
            }
            if (!file.exists()) {
                return R.fail("文件不存在");
            }
            if (file.delete()) {
                return R.ok("文件删除成功");
            } else {
                return R.fail("文件删除失败");
            }
        } catch (Exception e) {
            return R.fail("删除文件时出错: " + e.getMessage());
        }
    }
}
