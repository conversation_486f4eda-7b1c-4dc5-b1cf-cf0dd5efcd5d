package com.qmqb.imp.system.mapper;

import com.qmqb.imp.system.domain.ScanProjectFile;
import com.qmqb.imp.system.domain.vo.ScanProjectFileVo;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Date;

/**
 * 扫描项目文件记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
public interface ScanProjectFileMapper extends BaseMapperPlus<ScanProjectFileMapper, ScanProjectFile, ScanProjectFileVo> {

    /**
     * 更新旧扫描记录为非最新状态
     * @param pid 项目ID
     * @param scanFileUrls 文件路径列表，为null时更新项目的所有文件记录
     * @return 更新记录数
     */
    int updatePreviousScanToOld(@Param("pid") Long pid, @Param("scanFileUrls") List<String> scanFileUrls);

    /**
     * 根据项目id和扫描版本查询文件列表
     * @param pid 项目id  
     * @param scanVersion 扫描版本
     * @return
     */
    List<ScanProjectFileVo> selectByPidAndVersion(@Param("pid") Long pid, @Param("scanVersion") Long scanVersion);

    /**
     * 根据状态统计文件数量
     * @param pid 项目id
     * @param status 状态
     * @return
     */
    Long countByStatus(@Param("pid") Long pid, @Param("status") String status);

    /**
     * 统计指定部门和状态的问题文件数量
     * @param deptId 部门ID
     * @param status 状态
     * @return
     */
    Long countByStatusAndDept(@Param("deptId") String deptId, @Param("status") String status);

    /**
     * 查询指派给特定用户的文件列表
     * @param handleUserId 处理人ID
     * @param assignUserId 指派人ID
     * @return
     */
    List<ScanProjectFileVo> selectByAssignedUser(@Param("handleUserId") Long handleUserId, @Param("assignUserId") Long assignUserId);

    /**
     * 根据项目ID和文件路径查询最新的旧数据记录
     * @param pid 项目ID
     * @param scanFileUrl 文件路径
     * @return
     */
    ScanProjectFile selectLatestByPidAndScanFileUrl(@Param("pid") Long pid, @Param("scanFileUrl") String scanFileUrl);

    /**
     * 更新文件状态为已处理
     * @param id 文件ID
     * @param handleTime 处理时间
     * @return
     */
    int updateFileStatusToHandled(@Param("id") Long id, @Param("handleTime") Date handleTime);
} 