package com.qmqb.imp.common.core.domain.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 代码明细查询请求实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CodeQueryDTO extends BasePageDTO {

    /**
     * 所属团队 下拉框选择
     *
     */
    private Long dept;

    /**
     * 提交人姓名
     */
    private String submitter;

    /**
     * 中文
     */
    private String submitterC;

    /**
     * 英文
     */
    private String submitterE;

    /**
     * 提交说明
     */
    private String submitterMessage;

    /**
     * 所属项目
     */
    private String project;

    /**
     * 分支
     */
    private String branch;

    /**
     * 提交开始时间
     */
    private String beginDate;

    /**
     * 提交解释时间
     */
    private String endDate;

    /**
     * 不含合并消息   false > 不勾选合并 true > 勾选合并
     */
    public Boolean merged;

    /**
     * 所属项目
     */
    private List<String> spaceNames;

    /**
     * 多个模糊查询
     */
    Set<String> submittersList;

    /**
     * 部门
     */
    Set<String> userDeptsList;

    /**
     * 负责开发组
     */
    private String pDevDept;
    /**
     * 负责测试组
     */
    private String pTestDept;
    /**
     * 大类业务
     */
    private Long pBroadBusiness;
    /**
     * 小类业务
     */
    private Long pNarrowBusiness;

    @Data
    public static class Submitters {

        private String submitter;
    }

}
